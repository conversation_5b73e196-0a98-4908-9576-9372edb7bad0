/// <summary>
/// 钢矩管
/// </summary>
[Serializable] //序列化标识，所有图元必须有这个标签
[DbElement("钢矩管", MajorType.CurtainWall)]
public class SteelTubePlanD : DbElement
{ 
            #region 正确版本 (不带定位点）
        #region 几何参数
        /// <summary>
        /// 矩管宽度
        /// </summary>
        private double _width = 60;
        /// <summary>
        /// 矩管宽度
        /// </summary>
        [Category("几何参数"), DisplayName("宽度"), Description("矩管宽度"), ReadOnly(false)]
        public double Width
        {
            get { return _width; }
            set
            {
                if (_width == value) return;
                TransManager.Instance().Push(a => Width = a, _width);
                _width = value;
                // 宽度修改时，下边中点不变，左右同时扩大
                _isGeometryOperation = false;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 矩管高度
        /// </summary>
        private double _height = 120;
        /// <summary>
        /// 矩管高度
        /// </summary>
        [Category("几何参数"), DisplayName("高度"), Description("矩管高度"), ReadOnly(false)]
        public double Height
        {
            get { return _height; }
            set
            {
                if (_height == value) return;
                TransManager.Instance().Push(a => Height = a, _height);
                _height = value;
                // 高度修改时，下边中点不变，向上扩大
                _isGeometryOperation = false;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 壁厚
        /// </summary>
        private double _thickness = 3;
        /// <summary>
        /// 壁厚
        /// </summary>
        [Category("几何参数"), DisplayName("壁厚"), Description("矩管壁厚"), ReadOnly(false)]
        public double Thickness
        {
            get { return _thickness; }
            set
            {
                if (_thickness == value) return;
                TransManager.Instance().Push(a => Thickness = a, _thickness);
                _thickness = value;
                // 壁厚改变时，圆角半径会自动重新计算
                _ctCutCal2D3D();
            }
        }

        /// <summary>
        /// 弯角半径
        /// </summary>
        private double _radius;
        /// <summary>
        /// 弯角半径 - 根据壁厚自动计算
        /// </summary>
        [Category("几何参数"), DisplayName("弯角半径"), Description("外弯角半径（根据壁厚自动计算）"), ReadOnly(true)]
        public double Radius
        {
            get { return CalculateRadius(); }
        }

        /// <summary>
        /// 根据壁厚自动计算圆角半径
        /// 规则：
        /// - 当壁厚t≤3时，r=2*t
        /// - 当3<t≤6时，r=2.5*t  
        /// - 当6<t≤10时，r=3*t
        /// - 当t>10时，r=3*t
        /// </summary>
        /// <returns>计算得到的圆角半径</returns>
        private double CalculateRadius()
        {
            if (_thickness <= 3)
            {
                return 2 * _thickness;
            }
            else if (_thickness <= 6)
            {
                return 2.5 * _thickness;
            }
            else if (_thickness <= 10)
            {
                return 3 * _thickness;
            }
            else
            {
                return 3 * _thickness;
            }
        }

        /// <summary> 
        /// 截面旋转角度
        /// </summary>
        public double _hoAngle;
        /// <summary>
        /// 截面旋转角度
        /// </summary>
        [Category("几何参数"), DisplayName("截面旋转"), Description("截面旋转角度"), ReadOnly(false)]
        public double HoAngle
        {
            get => _hoAngle;
            set
            {
                if (Math.Abs(_hoAngle - value) < 0.001) return;
                TransManager.Instance().Push(a => HoAngle = a, _hoAngle);

                // 如果有变换矩阵，需要将新角度整合到变换矩阵中
                if (_hasTransform)
                {
                    // 先将当前角度整合到变换矩阵中
                    IntegrateRotationIntoTransform();

                    // 计算角度差值
                    double angleDiff = value - _hoAngle;

                    // 创建新的旋转矩阵
                    double angleRad = angleDiff * Math.PI / 180.0;
                    double cos = Math.Cos(angleRad);
                    double sin = Math.Sin(angleRad);

                    Matrix3D rotationMatrix = new Matrix3D(
                        cos, -sin, 0, 0,
                        sin, cos, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1
                    );

                    // 将新的旋转矩阵整合到变换矩阵中
                    _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);

                    // 重置角度，因为角度信息已经整合到变换矩阵中
                    _hoAngle = 0;
                }
                else
                {
                    // 没有变换矩阵时，直接设置角度
                    _hoAngle = value;
                }

                // 角度修改时几何中心不变，只需要重新计算控制点
                _needRecalcCenter = false; // 几何中心不需要重新计算
                _isGeometryOperation = false; // 参数修改，不是几何操作

                if (!AutoActivate) return;
                ActCutCal2D3D();
            }
        }

        /// <summary> 
        /// 是否填充
        /// </summary>
        public bool _ifHatch = true;
        /// <summary>
        /// 是否填充
        /// </summary>
        [Category("显示属性"), DisplayName("是否填充"), Description("钢矩管截面是否显示填充"), ReadOnly(false)]
        public bool IfHatch
        {
            get { return _ifHatch; }
            set
            {
                if (_ifHatch == value) return;
                TransManager.Instance().Push(a => IfHatch = a, _ifHatch);
                _ifHatch = value;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 是否显示中心线
        /// </summary>
        private bool _ifShowCenterLine = false;
        /// <summary>
        /// 是否显示中心线
        /// </summary>
        [Category("显示属性"), DisplayName("显示中心线"), Description("是否显示矩管中心十字线"), ReadOnly(false)]
        public bool IfShowCenterLine
        {
            get { return _ifShowCenterLine; }
            set
            {
                if (_ifShowCenterLine == value) return;
                TransManager.Instance().Push(a => IfShowCenterLine = a, _ifShowCenterLine);
                _ifShowCenterLine = value;
                ActCutCal2D3D();
            }
        }

        #endregion

        #region 偏移参数
        /// <summary>
        /// 左偏移 - 插入点到几何中心左侧的距离
        /// </summary>
        public double _leftOffset = 0;
        /// <summary>
        /// 左偏移
        /// </summary>
        [Category("位置控制"), DisplayName("左偏移"), Description("插入点到几何中心左侧的距离"), ReadOnly(false), Browsable(false)]
        public double LeftOffset
        {
            get => _leftOffset;
            set
            {
                if (Math.Abs(_leftOffset - value) < 0.001) return;
                TransManager.Instance().Push(a => LeftOffset = a, _leftOffset);
                _leftOffset = value;
                _needRecalcCenter = true; // 偏移参数改变时需要重新计算几何中心
                _isGeometryOperation = false; // 参数修改，不是几何操作
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 右偏移 - 插入点到几何中心右侧的距离
        /// </summary>
        public double _rightOffset = 0;
        /// <summary>
        /// 右偏移
        /// </summary>
        [Category("位置控制"), DisplayName("右偏移"), Description("插入点到几何中心右侧的距离"), ReadOnly(false), Browsable(false)]
        public double RightOffset
        {
            get => _rightOffset;
            set
            {
                if (Math.Abs(_rightOffset - value) < 0.001) return;
                TransManager.Instance().Push(a => RightOffset = a, _rightOffset);
                _rightOffset = value;
                _needRecalcCenter = true; // 偏移参数改变时需要重新计算几何中心
                _isGeometryOperation = false; // 参数修改，不是几何操作
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 上偏移 - 插入点到几何中心上侧的距离
        /// </summary>
        public double _topOffset = 0;
        /// <summary>
        /// 上偏移
        /// </summary>
        [Category("位置控制"), DisplayName("上偏移"), Description("插入点到几何中心上侧的距离"), ReadOnly(false), Browsable(false)]
        public double TopOffset
        {
            get => _topOffset;
            set
            {
                if (Math.Abs(_topOffset - value) < 0.001) return;
                TransManager.Instance().Push(a => TopOffset = a, _topOffset);
                _topOffset = value;
                _needRecalcCenter = true; // 偏移参数改变时需要重新计算几何中心
                _isGeometryOperation = false; // 参数修改，不是几何操作
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 下偏移 - 插入点到几何中心下侧的距离
        /// </summary>
        public double _bottomOffset = 90;
        /// <summary>
        /// 下偏移
        /// </summary>
        [Category("位置控制"), DisplayName("下偏移"), Description("插入点到几何中心下侧的距离"), ReadOnly(false), Browsable(false)]
        public double BottomOffset
        {
            get => _bottomOffset;
            set
            {
                if (Math.Abs(_bottomOffset - value) < 0.001) return;
                TransManager.Instance().Push(a => BottomOffset = a, _bottomOffset);
                _bottomOffset = value;
                _needRecalcCenter = true; // 偏移参数改变时需要重新计算几何中心
                _isGeometryOperation = false; // 参数修改，不是几何操作
                ActCutCal2D3D();
            }
        }
        #endregion

        #region 核心属性
        /// <summary>
        /// 插入点（用户指定的基准点）- 固定在矩管下边中点往下30mm处
        /// </summary>
        public DbPt _insertPt = new DbPt();

        /// <summary>
        /// 下边中点（矩管的基准点）- 从插入点向上偏移30mm
        /// </summary>
        public DbPt _bottomCenterPt = new DbPt();

        /// <summary>
        /// 是否正在进行几何操作（移动、旋转、镜像）
        /// true: 几何操作，直接使用操作后的控制点
        /// false: 参数修改，需要清空控制点重新计算
        /// </summary>
        private bool _isGeometryOperation = false;

        /// <summary>
        /// 变换矩阵 - 用于跟踪矩管的完整变换状态（镜像）
        /// </summary>
        private Matrix3D _transformMatrix = Matrix3D.Identity;

        /// <summary>
        /// 是否应用了变换矩阵
        /// </summary>
        private bool _hasTransform = false;

        #endregion

        #region 构造函数
        /// <summary>
        /// 无参构造函数，每个图元必须保留无参构造函数
        /// </summary>
        public SteelTube()
        {
            _if3D = false;
            LayerSet("幕墙龙骨");
        }

        /// <summary>
        /// 标准构造函数
        /// </summary>
        /// <param name="insertPt">插入点</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="thickness">壁厚</param>
        /// <param name="hoAngle">截面旋转角度</param>
        /// <param name="leftOffset">左偏移</param>
        /// <param name="rightOffset">右偏移</param>
        /// <param name="topOffset">上偏移</param>
        /// <param name="bottomOffset">下偏移</param>
        public SteelTube(DbPt insertPt, double width = 60.0, double height = 120.0, double thickness = 3.0,
                               double hoAngle = 0.0, double leftOffset = 30, double rightOffset = 30,
                               double topOffset = 0, double bottomOffset = -30)
        {
            _if3D = false;
            LayerSet("幕墙龙骨");
            _width = width;
            _height = height;
            _thickness = thickness;
            _hoAngle = hoAngle;
            _insertPt = insertPt;
            _leftOffset = leftOffset;
            _rightOffset = rightOffset;
            _topOffset = topOffset;
            _bottomOffset = bottomOffset;
            _ifHatch = true;

            // 初始化时需要计算几何中心
            _needRecalcCenter = true;
            _isGeometryOperation = false; // 初始化不是几何操作

            // 计算几何中心点，控制点的创建留给Activate()方法
            CalcuPtcenter();
        }

        /// <summary>
        /// 简化构造函数 - 插入点默认在矩管下边线偏移90mm处
        /// </summary>
        /// <param name="insertPt">插入点</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <param name="thickness">壁厚</param>
        /// <param name="hoAngle">截面旋转角度</param>
        public SteelTube(DbPt insertPt, double width, double height, double thickness = 3.0,
                               double hoAngle = 0.0)
            : this(insertPt, width, height, thickness, hoAngle, 30, 30, 0, -30)
        {
        }
        #endregion

        #region 核心计算方法
        /// <summary>
        /// 计算几何中心点 
        /// 根据插入点和偏移参数，通过象限判断算法计算几何中心位置
        /// </summary>
        public void CalcuPtcenter()
        {
            // 核心思想：根据左右偏移和上下偏移的大小关系，判断插入点在哪个象限
            // 然后计算从插入点到中心点的向量偏移

            // 第一象限判断：插入点相对于中心控制点在右上象限
            // 条件：左偏移≥右偏移 且 下偏移≥上偏移
            if (_leftOffset >= _rightOffset && _bottomOffset >= _topOffset)
            {
                // 计算逻辑：从插入点向左下移动到中心点
                // X方向：向左移动 (矩管宽度/2 - 右偏移) 的距离
                // Y方向：向下移动 (矩管高度/2 - 上偏移) 的距离
                _centerPt = _insertPt.Move(-(_width / 2 - _rightOffset), -(_height / 2 - _topOffset));
            }
            // 第二象限判断：插入点相对于中心控制点在左上象限
            // 条件：左偏移≤右偏移 且 上偏移≤下偏移
            else if (_leftOffset <= _rightOffset && _topOffset <= _bottomOffset)
            {
                // 计算逻辑：从插入点向右下移动到中心点
                // X方向：向右移动 (矩管宽度/2 - 左偏移) 的距离
                // Y方向：向下移动 (矩管高度/2 - 上偏移) 的距离
                _centerPt = _insertPt.Move(_width / 2 - _leftOffset, -(_height / 2 - _topOffset));
            }
            // 第三象限判断：插入点相对于中心控制点在左下象限
            // 条件：左偏移≤右偏移 且 上偏移≥下偏移
            else if (_leftOffset <= _rightOffset && _topOffset >= _bottomOffset)
            {
                // 计算逻辑：从插入点向右上移动到中心点
                // X方向：向右移动 (矩管宽度/2 - 左偏移) 的距离
                // Y方向：向上移动 (矩管高度/2 - 下偏移) 的距离
                _centerPt = _insertPt.Move((_width / 2 - _leftOffset), (_height / 2 - _bottomOffset));
            }
            // 第四象限判断：插入点相对于中心控制点在右下象限
            // 条件：上偏移≥下偏移 且 左偏移≥右偏移
            else if (_topOffset >= _bottomOffset && _leftOffset >= _rightOffset)
            {
                // 计算逻辑：从插入点向左上移动到中心点
                // X方向：向左移动 (矩管宽度/2 - 右偏移) 的距离
                // Y方向：向上移动 (矩管高度/2 - 下偏移) 的距离
                _centerPt = _insertPt.Move(-(_width / 2 - _rightOffset), _height / 2 - _bottomOffset);
            }
            else
            {
                // 异常情况处理：如果偏移参数不符合任何象限判断条件
                // 直接使用插入点作为中心点（容错处理）
                _centerPt = _insertPt;
            }

            // 注意：不再直接操作ConPts数组，因为CalcuPts()会重新创建整个数组
            // 几何中心的设置由CalcuPts()方法负责
        }

        /// <summary>
        /// 计算所有控制点 - 基于当前几何中心位置
        /// 圆角矩管不存储直角角点，只存储边中点和圆弧控制点
        /// </summary>
        public void CalcuPts()
        {
            // 清除所有控制点
            ConPts.Clear();

            // 确保几何中心作为第一个控制点
            DbPt currentCenter = _centerPt.EleCopy();
            ConPts.Add(currentCenter);

            double halfWidth = _width / 2.0;
            double halfHeight = _height / 2.0;

            // 添加边中点（用于拉伸控制），顺序要与EleMove_s方法中的逻辑匹配
            // ConPts[1]: 上边中点
            DbPt topMid = new DbPt(currentCenter.X, currentCenter.Y + halfHeight);
            topMid.PtType = 2; // 标记为边中点
            ConPts.Add(topMid);

            // ConPts[2]: 右边中点
            DbPt rightMid = new DbPt(currentCenter.X + halfWidth, currentCenter.Y);
            rightMid.PtType = 2; // 标记为边中点
            ConPts.Add(rightMid);

            // ConPts[3]: 下边中点
            DbPt bottomMid = new DbPt(currentCenter.X, currentCenter.Y - halfHeight);
            bottomMid.PtType = 2; // 标记为边中点
            ConPts.Add(bottomMid);

            // ConPts[4]: 左边中点
            DbPt leftMid = new DbPt(currentCenter.X - halfWidth, currentCenter.Y);
            leftMid.PtType = 2; // 标记为边中点
            ConPts.Add(leftMid);

            // 如果有圆角，添加圆弧控制点
            if (CalculateRadius() > 0.001)
            {
                // 计算四个直角角点（仅用于圆弧计算，不存储到ConPts中）
                DbPt[] corners = new DbPt[]
                {
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y - halfHeight), // 左下
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y - halfHeight), // 右下
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y + halfHeight), // 右上
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y + halfHeight), // 左上
                };

                // 添加圆弧控制点
                AddArcControlPoints(corners);
            }

            // 应用所有变换（旋转+镜像）
            ApplyAllTransforms();
        }

        /// <summary>
        /// 应用所有变换（旋转+镜像）到控制点
        /// </summary>
        private void ApplyAllTransforms()
        {
            if (ConPts.Count <= 1) return;

            DbPt center = ConPts[0];

            // 对除几何中心外的所有控制点应用变换
            for (int i = 1; i < ConPts.Count; i++)
            {
                // 1. 先应用旋转变换（如果有旋转角度）
                if (Math.Abs(_hoAngle) > 0.001)
                {
                    double angleRad = _hoAngle * Math.PI / 180.0;
                    ConPts[i].RotateSelf(center, angleRad);
                }

                // 2. 再应用变换矩阵（镜像等变换）
                if (_hasTransform)
                {
                    ApplyTransformToPoint(ConPts[i], center);
                }
            }
        }

        /// <summary>
        /// 应用变换矩阵到所有控制点（除几何中心外）
        /// </summary>
        //private void ApplyTransformMatrix()
        //{
        //    if (!_hasTransform || ConPts.Count <= 1) return;

        //    DbPt center = ConPts[0]; // 几何中心作为变换原点

        //    // 对除几何中心外的所有控制点应用变换
        //    for (int i = 1; i < ConPts.Count; i++)
        //    {
        //        // 将点转换为相对于几何中心的坐标
        //        double relativeX = ConPts[i].X - center.X;
        //        double relativeY = ConPts[i].Y - center.Y;

        //        // 应用变换矩阵
        //        double transformedX = _transformMatrix.M11 * relativeX + _transformMatrix.M12 * relativeY;
        //        double transformedY = _transformMatrix.M21 * relativeX + _transformMatrix.M22 * relativeY;

        //        // 转换回全局坐标
        //        ConPts[i].X = center.X + transformedX;
        //        ConPts[i].Y = center.Y + transformedY;
        //    }
        //}

        /// <summary>
        /// 添加圆弧控制点
        /// </summary>
        /// <param name="corners">四个角点：左下、右下、右上、左上</param>
        private void AddArcControlPoints(DbPt[] corners)
        {
            double radius = CalculateRadius();
            if (radius < 0.001) return;

            // 为每个角添加圆弧控制点
            for (int i = 0; i < 4; i++)
            {
                DbPt currentCorner = corners[i];
                DbPt prevCorner = corners[(i + 3) % 4]; // 上一个角点
                DbPt nextCorner = corners[(i + 1) % 4]; // 下一个角点

                //计算从当前角点到相邻角点的方向向量
                DbLine l1 = new DbLine(currentCorner, prevCorner);
                DbLine l2 = new DbLine(currentCorner, nextCorner);

                //归一化
                DbPt dirPre = l1.PtEnd - l1.PtSt;
                DbPt dirNext = l2.PtEnd - l2.PtSt;
                dirPre.Normalize2D();
                dirNext.Normalize2D();

                // 倒角三个关键点计算
                // 1. 倒角起点：从角点沿第一条边向内移动倒角半径
                DbPt arcStart = new DbPt(currentCorner.X + dirPre.X * radius, currentCorner.Y + dirPre.Y * radius);
                arcStart.PtType = 0;

                // 2. 倒角终点：从角点沿第二条边向内移动倒角半径
                DbPt arcEnd = new DbPt(currentCorner.X + dirNext.X * radius, currentCorner.Y + dirNext.Y * radius);
                arcEnd.PtType = 0;

                // 3. 倒角圆心：对于90度直角，圆心位于角点向内偏移倒角半径的位置
                //    圆心 = 角点 + (两个方向向量之和) × 倒角半径
                DbPt arcCenter = new DbPt(currentCorner.X + (dirPre.X + dirNext.X) * radius,
                                         currentCorner.Y + (dirPre.Y + dirNext.Y) * radius);

                //DbLine arc = GMath.GetArcByStCenEnd(arcStart, arcCenter, arcEnd);
                //DbPt arcMid = arc.PtMid.EleCopy();
                DbPt arcMid = CalculateOutwardArcMid(arcStart, arcEnd, arcCenter, currentCorner);
                arcMid.PtType = 1; //圆弧中点标记为1

                //添加点
                ConPts.Add(arcStart);
                ConPts.Add(arcMid);
                ConPts.Add(arcEnd);
            }
        }

        /// <summary>
        /// 计算向外凸出的圆弧中点
        /// </summary>
        /// <param name="arcStart">圆弧起点</param>
        /// <param name="arcEnd">圆弧终点</param>
        /// <param name="arcCenter">圆弧圆心</param>
        /// <param name="cornerPt">角点（用于确定向外方向）</param>
        /// <returns>向外凸出的圆弧中点</returns>
        private DbPt CalculateOutwardArcMid(DbPt arcStart, DbPt arcEnd, DbPt arcCenter, DbPt cornerPt)
        {
            // 计算起点和终点的中点
            DbPt midPoint = new DbPt((arcStart.X + arcEnd.X) / 2.0, (arcStart.Y + arcEnd.Y) / 2.0);

            // 计算从圆心到中点的向量
            DbPt centerToMid = new DbPt(midPoint.X - arcCenter.X, midPoint.Y - arcCenter.Y);

            // 计算从角点到圆心的向量
            DbPt cornerToCenter = new DbPt(arcCenter.X - cornerPt.X, arcCenter.Y - cornerPt.Y);

            // 归一化向量
            centerToMid.Normalize2D();
            cornerToCenter.Normalize2D();

            // 计算圆弧半径
            double radius = GMath.Distance(arcCenter, arcStart);

            // 如果从圆心到中点的方向与从角点到圆心的方向相同，说明圆弧向内凸
            // 需要反向，使其向外凸
            double dotProduct = centerToMid.X * cornerToCenter.X + centerToMid.Y * cornerToCenter.Y;

            if (dotProduct > 0) // 同向，需要反向
            {
                centerToMid.X = -centerToMid.X;
                centerToMid.Y = -centerToMid.Y;
            }

            // 计算向外凸出的圆弧中点
            DbPt outwardMid = new DbPt(
                arcCenter.X + centerToMid.X * radius,
                arcCenter.Y + centerToMid.Y * radius
            );

            return outwardMid;
        }

        /// <summary>
        /// 获取外轮廓点集 - 基于当前几何中心位置
        /// </summary>
        /// <returns>包含弧形中点的外轮廓点集</returns>
        private List<DbPt> GetOuterPoints()
        {
            List<DbPt> points = new List<DbPt>();

            // 如果没有圆弧（半径为0），直接返回四个角点
            if (CalculateRadius() < 0.001)
            {
                double halfWidth = _width / 2.0;
                double halfHeight = _height / 2.0;

                // 使用当前的几何中心位置计算四个角点
                DbPt currentCenter = _centerPt.EleCopy();
                DbPt[] corners = new DbPt[]
                {
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y - halfHeight), // 左下
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y - halfHeight), // 右下
                new DbPt(currentCenter.X + halfWidth, currentCenter.Y + halfHeight), // 右上
                new DbPt(currentCenter.X - halfWidth, currentCenter.Y + halfHeight), // 左上
                };

                // 应用变换
                if (_hasTransform)
                {
                    // 有变换矩阵时，直接应用变换矩阵（包含了旋转和镜像信息）
                    for (int i = 0; i < corners.Length; i++)
                    {
                        ApplyTransformToPoint(corners[i], currentCenter);
                    }
                }
                else if (Math.Abs(_hoAngle) > 0.001)
                {
                    // 没有变换矩阵时，只应用旋转角度
                    double angleRad = _hoAngle * Math.PI / 180.0;
                    for (int i = 0; i < corners.Length; i++)
                    {
                        corners[i].RotateSelf(currentCenter, angleRad);
                    }
                }

                points.AddRange(corners);

                // 确保点集是逆时针方向
                EnsureCounterClockwise(points);
                return points;
            }

            // 从控制点中提取外轮廓点（跳过几何中心和边中点）
            // ConPts[0]: 几何中心
            // ConPts[1-4]: 边中点 (PtType = 2)
            // ConPts[5+]: 圆弧控制点
            for (int i = 5; i < ConPts.Count; i++)
            {
                if (ConPts[i].PtType != 2) // 不是边中点
                {
                    points.Add(ConPts[i]);
                }
            }

            // 确保点集是逆时针方向
            EnsureCounterClockwise(points);
            return points;
        }

        /// <summary>
        /// 确保点集是逆时针方向
        /// </summary>
        /// <param name="points">点集</param>
        private void EnsureCounterClockwise(List<DbPt> points)
        {
            if (points.Count < 3) return;

            // 计算点集的有向面积（使用叉积）
            double signedArea = 0;
            for (int i = 0; i < points.Count; i++)
            {
                int j = (i + 1) % points.Count;
                signedArea += (points[j].X - points[i].X) * (points[j].Y + points[i].Y);
            }

            // 如果有向面积为正，说明是顺时针，需要反转
            if (signedArea > 0)
            {
                points.Reverse();
            }
        }


        /// <summary>
        /// 将点集转换为线段
        /// </summary>
        /// <param name="points">包含弧形中点的点集</param>
        private void ConvertPointsToLines(List<DbPt> points)
        {
            if (points == null || points.Count < 3) return;

            for (int i = 0; i < points.Count; i++)
            {
                DbPt currentPt = points[i];
                DbPt nextPt = points[(i + 1) % points.Count];

                if (nextPt.PtType == 1) // 下一个点是圆弧中点
                {
                    // 创建圆弧线段
                    DbPt endPt = points[(i + 2) % points.Count];
                    DbLine arcLine = new DbLine(currentPt.EleCopy(), endPt.EleCopy(), nextPt.EleCopy());
                    Lines.Add(arcLine);
                    i++; // 跳过圆弧中点
                }
                else
                {
                    // 创建直线段
                    DbLine line = new DbLine(currentPt.EleCopy(), nextPt.EleCopy());
                    Lines.Add(line);
                }
            }
        }

        /// <summary>
        /// 添加中心线
        /// </summary>
        private void AddCenterLine()
        {
            DbPt center = _centerPt.EleCopy();
            double halfWidth = _width / 2.0 + 10;
            double halfHeight = _height / 2.0 + 10;

            // 添加水平中心线
            DbPt hStart = new DbPt(center.X - halfWidth, center.Y);
            DbPt hEnd = new DbPt(center.X + halfWidth, center.Y);

            // 添加垂直中心线
            DbPt vStart = new DbPt(center.X, center.Y - halfHeight);
            DbPt vEnd = new DbPt(center.X, center.Y + halfHeight);

            //应用旋转
            if (Math.Abs(_hoAngle) > 0.001)
            {
                double angleRad = _hoAngle * Math.PI / 180.0;
                hStart.RotateSelf(center, angleRad);
                hEnd.RotateSelf(center, angleRad);
                vStart.RotateSelf(center, angleRad);
                vEnd.RotateSelf(center, angleRad);
            }

            // 应用变换矩阵
            if (_hasTransform)
            {
                ApplyTransformToPoint(hStart, center);
                ApplyTransformToPoint(hEnd, center);
                ApplyTransformToPoint(vStart, center);
                ApplyTransformToPoint(vEnd, center);
            }

            DbLine hLine = new DbLine(hStart, hEnd);
            DbLine vLine = new DbLine(vStart, vEnd);

            hLine.LayerId = PreLayerManage.GetLayerId("通用-非打印");
            vLine.LayerId = PreLayerManage.GetLayerId("通用-非打印");
            hLine.SetStatus(0, 2, 0);
            vLine.SetStatus(0, 2, 0);
            hLine.StyleIndex = 2; // 默认样式
            vLine.StyleIndex = 2; // 默认样式

            Lines.Add(hLine);
            Lines.Add(vLine);
        }
        #endregion

        #region 图元操作方法
        /// <summary>
        /// 图元激活计算 - 简化的控制点管理
        /// </summary>
        public override void Activate()
        {
            Hatchs.Clear();
            Lines.Clear();

            // 根据操作类型决定控制点的处理方式
            if (_isGeometryOperation)
            {
                // 几何操作：直接使用操作后的控制点
                if (ConPts.Count > 0)
                {
                    _bottomCenterPt = ConPts[0].EleCopy();
                    ExtractTransformFromControlPoints();
                }
            }
            else
            {
                // 参数修改或初始化：重新计算控制点
                CalcuBottomCenter();
                CalcuPts();
            }

            // 获取外轮廓点集
            List<DbPt> outerPoints = GetOuterPoints();

            // 获取内轮廓点集
            List<DbPt> innerPoints = GMath.GetOffsetArcPts(outerPoints, _thickness, true);

            // 转换为线段
            ConvertPointsToLines(outerPoints);
            ConvertPointsToLines(innerPoints);

            // 添加填充
            if (_ifHatch)
            {
                DbHatch hatch = new DbHatch(Lines, 1, 1);
                hatch.LayerId = PreLayerManage.GetLayerId("填充层");
                Hatchs.Add(hatch);
            }

            // 设置线段属性
            foreach (DbLine line in Lines)
            {
                line.SetStatus(1, 1, 1);
                line.LayerId = PreLayerManage.GetLayerId("幕墙龙骨");
            }

            LayerChange(layerManage.GetLayer(_layerId));

            if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
            if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
            if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

            // 添加中心线（如果需要）
            if (_ifShowCenterLine)
            {
                AddCenterLine();
            }

            EleArea = new DbEleArea(this);
            CalSolid2D();

            // 重置操作状态
            _isGeometryOperation = false; // 重置几何操作标志
        }

        /// <summary>
        /// 返回图元被单独选中时显示的提示内容
        /// </summary>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = "简化矩管";
            str2 = $"{_width:F0}×{_height:F0}×{_thickness:F0}";
        }

        /// <summary>
        /// 重写移动 - 参考建筑柱的实现方式，直接操作所有控制点
        /// </summary>
        public override void EleMove(double X, double Y)
        {
            // 直接移动所有控制点（包括几何中心）
            foreach (DbPt pt in ConPts)
            {
                pt.MoveSelf(X, Y);
            }

            // 移动后更新几何中心点（保持一致性）
            if (ConPts.Count > 0)
            {
                _centerPt = ConPts[0].EleCopy();
            }

            // 移动操作不改变变换矩阵，因为它只是平移

            _isGeometryOperation = true; // 标记为几何操作
            Activate();
        }

        /// <summary>
        /// 图元拉伸控制 - 基于下边中点的拉伸逻辑
        /// </summary>
        public override void EleMove_s(double X, double Y)
        {
            if (ConPts == null || ConPts.Count == 0) return;

            // 下边中点被拖拽：整体移动
            if (ConPts[0].Status == 1)
            {
                EleMove(X, Y);
                return;
            }

            // 其他控制点被拖拽
            if (ConPts.Count > 4)
            {
                // 上边中点：高度调整（向上扩大）
                if (ConPts[1].Status == 1)
                {
                    double localDeltaY = CalculateLocalDeltaWithTransform(X, Y, true);
                    ConPts[1].MoveSelf(X, Y);

                    // 计算拖动方向
                    DbPt dragVector = new DbPt(X, Y);
                    double dotProduct = dragVector.Y; // 简化：只看Y方向

                    // 高度变化：向上为正，向下为负
                    double heightChange = Math.Abs(localDeltaY) * (dotProduct > 0 ? 1 : -1);
                    _height = Math.Max(10, _height + heightChange);

                    // 下边中点保持不变，重新计算其他控制点
                    RecalculateFromBottomCenter();
                }
                // 右边中点：宽度调整（双边扩大）
                else if (ConPts[2].Status == 1)
                {
                    double localDeltaX = CalculateLocalDeltaWithTransform(X, Y, false);
                    ConPts[2].MoveSelf(X, Y);

                    DbPt dragVector = new DbPt(X, Y);
                    double dotProduct = dragVector.X;

                    double widthChange = Math.Abs(localDeltaX) * (dotProduct > 0 ? 2 : -2);
                    _width = Math.Max(10, _width + widthChange);

                    RecalculateFromBottomCenter();
                }
                // 左边中点：宽度调整（双边扩大）
                else if (ConPts[3].Status == 1)
                {
                    double localDeltaX = CalculateLocalDeltaWithTransform(X, Y, false);
                    ConPts[3].MoveSelf(X, Y);

                    DbPt dragVector = new DbPt(X, Y);
                    double dotProduct = -dragVector.X; // 左边相反

                    double widthChange = Math.Abs(localDeltaX) * (dotProduct > 0 ? 2 : -2);
                    _width = Math.Max(10, _width + widthChange);

                    RecalculateFromBottomCenter();
                }
                // 几何中心：整体移动（保持下边中点相对位置）
                else if (ConPts[4].Status == 1)
                {
                    // 几何中心移动时，下边中点也要相应移动
                    ConPts[0].MoveSelf(X, Y); // 下边中点跟随移动
                    ConPts[4].MoveSelf(X, Y); // 几何中心移动
                    _bottomCenterPt = ConPts[0].EleCopy();
                    
                    RecalculateFromBottomCenter();
                }
            }

            _isGeometryOperation = true;
            Activate();
        }

        /// <summary>
        /// 计算考虑变换的本地坐标增量（扩展版CalculateLocalDelta）
        /// </summary>
        /// <param name="globalX">全局X方向拖拽距离</param>
        /// <param name="globalY">全局Y方向拖拽距离</param>
        /// <param name="isVertical">true表示计算垂直方向，false表示水平方向</param>
        /// <returns>本地坐标系的距离</returns>
        private double CalculateLocalDeltaWithTransform(double globalX, double globalY, bool isVertical)
        {
            // 创建全局移动向量
            DbPt globalDelta = new DbPt(globalX, globalY);

            // 计算标准方向
            DbPt standardDirection;
            if (isVertical)
            {
                // 垂直方向
                standardDirection = new DbPt(0, 1);
            }
            else
            {
                // 水平方向
                standardDirection = new DbPt(1, 0);
            }

            // 应用变换得到实际方向
            DbPt actualDirection;
            if (_hasTransform)
            {
                // 有变换矩阵时，直接应用变换矩阵（包含了旋转和镜像信息）
                actualDirection = new DbPt(
                    _transformMatrix.M11 * standardDirection.X + _transformMatrix.M12 * standardDirection.Y,
                    _transformMatrix.M21 * standardDirection.X + _transformMatrix.M22 * standardDirection.Y
                );
            }
            else if (Math.Abs(_hoAngle) > 0.001)
            {
                // 没有变换矩阵时，只应用旋转角度
                double angleRad = _hoAngle * Math.PI / 180.0;
                if (isVertical)
                {
                    // 垂直方向：考虑旋转角度的垂直方向
                    actualDirection = new DbPt(-Math.Sin(angleRad), Math.Cos(angleRad));
                }
                else
                {
                    // 水平方向：考虑旋转角度的水平方向
                    actualDirection = new DbPt(Math.Cos(angleRad), Math.Sin(angleRad));
                }
            }
            else
            {
                // 没有任何变换时，使用标准方向
                actualDirection = standardDirection;
            }

            // 计算全局移动向量在实际方向上的投影
            double dotProduct = globalDelta.X * actualDirection.X + globalDelta.Y * actualDirection.Y;
            double actualDirectionLength = Math.Sqrt(actualDirection.X * actualDirection.X + actualDirection.Y * actualDirection.Y);

            // 返回投影长度（带符号）
            return actualDirectionLength > 0.001 ? dotProduct / actualDirectionLength : 0;
        }

        /// <summary>
        /// 获取简化的垂直方向（只考虑主要变换）
        /// </summary>
        /// <returns>简化的垂直方向单位向量</returns>
        private DbPt GetSimpleVerticalDirection()
        {
            // 标准垂直方向
            DbPt verticalDir = new DbPt(0, 1);

            // 应用变换
            if (_hasTransform)
            {
                // 有变换矩阵时，直接应用变换矩阵（包含了旋转和镜像信息）
                double transformedX = _transformMatrix.M11 * verticalDir.X + _transformMatrix.M12 * verticalDir.Y;
                double transformedY = _transformMatrix.M21 * verticalDir.X + _transformMatrix.M22 * verticalDir.Y;
                verticalDir.X = transformedX;
                verticalDir.Y = transformedY;
            }
            else if (Math.Abs(_hoAngle) > 0.001)
            {
                // 没有变换矩阵时，只应用旋转角度
                double angleRad = _hoAngle * Math.PI / 180.0;
                double newX = verticalDir.X * Math.Cos(angleRad) - verticalDir.Y * Math.Sin(angleRad);
                double newY = verticalDir.X * Math.Sin(angleRad) + verticalDir.Y * Math.Cos(angleRad);
                verticalDir.X = newX;
                verticalDir.Y = newY;
            }

            verticalDir.Normalize2D();
            return verticalDir;
        }

        /// <summary>
        /// 计算全局拖拽向量在矩管本地坐标系中的投影
        /// </summary>
        /// <param name="globalX">全局X方向拖拽距离</param>
        /// <param name="globalY">全局Y方向拖拽距离</param>
        /// <param name="isVertical">true表示计算垂直方向，false表示水平方向</param>
        /// <returns>本地坐标系的距离</returns>
        private double CalculateLocalDelta(double globalX, double globalY, bool isVertical)
        {
            if (Math.Abs(_hoAngle) < 0.001)
            {
                return isVertical ? globalY : globalX;
            }

            double angleRad = _hoAngle * Math.PI / 180.0;
            double cosAngle = Math.Cos(angleRad);
            double sinAngle = Math.Sin(angleRad);

            if (isVertical)
            {
                // 垂直方向的单位向量（Y轴旋转后）
                return globalX * (-sinAngle) + globalY * cosAngle;
            }
            else
            {
                // 水平方向的单位向量（X轴旋转后）
                return globalX * cosAngle + globalY * sinAngle;
            }
        }

        /// <summary>
        /// 计算点到几何中心的距离（考虑旋转）
        /// </summary>
        /// <param name="point">目标点</param>
        /// <param name="center">几何中心</param>
        /// <param name="isVertical">true表示计算垂直方向距离，false表示水平方向距离</param>
        /// <returns>距离值</returns>
        private double CalculateDistanceFromCenter(DbPt point, DbPt center, bool isVertical)
        {
            if (Math.Abs(_hoAngle) < 0.001)
            {
                // 无旋转时直接计算
                return isVertical ? Math.Abs(point.Y - center.Y) : Math.Abs(point.X - center.X);
            }

            // 有旋转时需要转换到本地坐标系
            double angleRad = _hoAngle * Math.PI / 180.0;
            double cosAngle = Math.Cos(-angleRad); // 反向旋转
            double sinAngle = Math.Sin(-angleRad);

            // 将点转换到本地坐标系（以几何中心为原点）
            double localX = (point.X - center.X) * cosAngle - (point.Y - center.Y) * sinAngle;
            double localY = (point.X - center.X) * sinAngle + (point.Y - center.Y) * cosAngle;

            return isVertical ? Math.Abs(localY) : Math.Abs(localX);
        }

        /// <summary>
        /// 基于下边中点重新计算所有控制点
        /// </summary>
        private void RecalculateFromBottomCenter()
        {
            DbPt bottomCenter = ConPts[0].EleCopy();
            _bottomCenterPt = bottomCenter;
            
            double halfWidth = _width / 2.0;
            double halfHeight = _height / 2.0;

            // 重新计算其他控制点位置
            ConPts[1] = new DbPt(bottomCenter.X, bottomCenter.Y + _height) { PtType = 2 }; // 上边中点
            ConPts[2] = new DbPt(bottomCenter.X + halfWidth, bottomCenter.Y + halfHeight) { PtType = 2 }; // 右边中点
            ConPts[3] = new DbPt(bottomCenter.X - halfWidth, bottomCenter.Y + halfHeight) { PtType = 2 }; // 左边中点
            ConPts[4] = new DbPt(bottomCenter.X, bottomCenter.Y + halfHeight) { PtType = 1 }; // 几何中心

            // 应用变换
            ApplyAllTransforms();
        }

        /// <summary>
        /// 对单个点应用变换矩阵
        /// </summary>
        /// <param name="point">要变换的点</param>
        /// <param name="center">变换中心</param>
        private void ApplyTransformToPoint(DbPt point, DbPt center)
        {
            // 将点转换为相对于中心的坐标
            double relativeX = point.X - center.X;
            double relativeY = point.Y - center.Y;

            // 应用变换矩阵
            double transformedX = _transformMatrix.M11 * relativeX + _transformMatrix.M12 * relativeY;
            double transformedY = _transformMatrix.M21 * relativeX + _transformMatrix.M22 * relativeY;

            // 转换回全局坐标
            point.X = center.X + transformedX;
            point.Y = center.Y + transformedY;
        }

        /// <summary>
        /// 重写旋转 - 参考建筑柱的实现方式，直接旋转所有控制点
        /// </summary>
        public override void EleMove_r(DbPt rotCenter, double angle)
        {
            // 直接旋转所有控制点
            foreach (DbPt pt in ConPts)
            {
                pt.RotateSelf(rotCenter, angle);
            }

            // 旋转后更新几何中心点（关键）
            if (ConPts.Count > 0)
            {
                _centerPt = ConPts[0].EleCopy();
            }

            // 如果已经有变换矩阵，需要将旋转整合到变换矩阵中
            if (_hasTransform)
            {
                // 先将当前旋转角度整合到变换矩阵中
                IntegrateRotationIntoTransform();

                // 再添加新的旋转
                double angleRad = angle;
                double cos = Math.Cos(angleRad);
                double sin = Math.Sin(angleRad);

                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );

                _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);
                _hoAngle = 0; // 重置角度，因为已经整合到变换矩阵中
            }
            else
            {
                // 没有变换矩阵时，直接更新旋转角度
                _hoAngle += angle * 180 / Math.PI;
            }

            // 标准化角度到 [-180, 180] 范围
            while (_hoAngle > 180) _hoAngle -= 360;
            while (_hoAngle < -180) _hoAngle += 360;

            _isGeometryOperation = true; // 标记为几何操作
            Activate();
        }

        /// <summary>
        /// 重写镜像 - 使用变换矩阵处理镜像变换
        /// </summary>
        public override void EleMove_m(DbLine mirrorLine)
        {
            // 直接镜像所有控制点
            foreach (DbPt pt in ConPts)
            {
                pt.MirrorSelf(mirrorLine);
            }

            // 镜像后更新几何中心点（关键修复）
            if (ConPts.Count > 0)
            {
                _centerPt = ConPts[0].EleCopy();
            }

            // 关键修复：将旋转角度信息整合到变换矩阵中
            IntegrateRotationIntoTransform();

            // 更新变换矩阵以包含镜像
            UpdateTransformMatrixForMirror(mirrorLine);

            // 镜像后重置旋转角度，因为旋转信息已经整合到变换矩阵中
            _hoAngle = 0;

            _isGeometryOperation = true; // 标记为几何操作
            Activate();
        }

        /// <summary>
        /// 将旋转角度信息整合到变换矩阵中
        /// </summary>
        private void IntegrateRotationIntoTransform()
        {
            if (Math.Abs(_hoAngle) > 0.001)
            {
                // 创建旋转矩阵
                double angleRad = _hoAngle * Math.PI / 180.0;
                double cos = Math.Cos(angleRad);
                double sin = Math.Sin(angleRad);

                Matrix3D rotationMatrix = new Matrix3D(
                    cos, -sin, 0, 0,
                    sin, cos, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );

                // 将旋转矩阵整合到现有变换矩阵中
                _transformMatrix = Matrix3D.Multiply(_transformMatrix, rotationMatrix);
                _hasTransform = true;
            }
        }

        /// <summary>
        /// 更新变换矩阵以包含镜像
        /// </summary>
        private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
        {
            // 计算镜像线的方向向量
            DbPt lineDir = mirrorLine.PtEnd - mirrorLine.PtSt;
            lineDir.Normalize2D();

            // 计算镜像线的法向量
            DbPt normal = new DbPt(-lineDir.Y, lineDir.X);

            // 创建镜像矩阵
            // 镜像矩阵 = I - 2 * n * n^T (其中n是单位法向量)
            double nx = normal.X;
            double ny = normal.Y;

            Matrix3D mirrorMatrix = new Matrix3D(
                1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
                -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
                0, 0, 1, 0,
                0, 0, 0, 1
            );

            // 组合变换矩阵
            _transformMatrix = Matrix3D.Multiply(_transformMatrix, mirrorMatrix);
            _hasTransform = true;
        }

        /// <summary>
        /// 从当前控制点提取变换矩阵
        /// 用于在几何操作后确保镜像变换状态正确
        /// 注意：这里不提取旋转信息，旋转由_hoAngle处理
        /// </summary>
        private void ExtractTransformFromControlPoints()
        {
            if (ConPts.Count < 5) return;

            // 使用边中点来计算当前的变换状态
            DbPt center = ConPts[0];
            DbPt rightPoint = ConPts[2]; // 右边中点
            DbPt topPoint = ConPts[1];   // 上边中点

            // 计算当前的X轴和Y轴方向向量
            DbPt xAxis = new DbPt(rightPoint.X - center.X, rightPoint.Y - center.Y);
            DbPt yAxis = new DbPt(topPoint.X - center.X, topPoint.Y - center.Y);

            // 归一化
            double xLength = Math.Sqrt(xAxis.X * xAxis.X + xAxis.Y * xAxis.Y);
            double yLength = Math.Sqrt(yAxis.X * yAxis.X + yAxis.Y * yAxis.Y);

            if (xLength > 0.001 && yLength > 0.001)
            {
                xAxis.X /= xLength;
                xAxis.Y /= xLength;
                yAxis.X /= yLength;
                yAxis.Y /= yLength;

                // 先从当前方向向量中移除旋转分量
                double currentAngle = _hoAngle * Math.PI / 180.0;
                double cos = Math.Cos(-currentAngle); // 反向旋转
                double sin = Math.Sin(-currentAngle);

                // 将轴向量转换到无旋转状态
                double xAxisX_noRot = xAxis.X * cos - xAxis.Y * sin;
                double xAxisY_noRot = xAxis.X * sin + xAxis.Y * cos;
                double yAxisX_noRot = yAxis.X * cos - yAxis.Y * sin;
                double yAxisY_noRot = yAxis.X * sin + yAxis.Y * cos;

                // 构建变换矩阵（不包含旋转）
                _transformMatrix = new Matrix3D(
                    xAxisX_noRot, yAxisX_noRot, 0, 0,
                    xAxisY_noRot, yAxisY_noRot, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );

                // 检查是否为单位矩阵（主要检测镜像）
                _hasTransform = Math.Abs(_transformMatrix.M11 - 1) > 0.001 ||
                               Math.Abs(_transformMatrix.M12) > 0.001 ||
                               Math.Abs(_transformMatrix.M21) > 0.001 ||
                               Math.Abs(_transformMatrix.M22 - 1) > 0.001;
            }
        }
        #endregion

        #region 数据持久化
        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter"></param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //图元类标识，必要
            binaryWriter.Write(this.GetType().ToString());

            //版本号，必要
            binaryWriter.Write(0);

            //图元共有参数，必要
            PubSave(binaryWriter);

            //图元特有参数
            binaryWriter.Write(_width);
            binaryWriter.Write(_height);
            binaryWriter.Write(_thickness);
            binaryWriter.Write(_hoAngle);
            binaryWriter.Write(_ifHatch);
            binaryWriter.Write(_leftOffset);
            binaryWriter.Write(_rightOffset);
            binaryWriter.Write(_topOffset);
            binaryWriter.Write(_bottomOffset);

            // 保存变换矩阵
            binaryWriter.Write(_hasTransform);
            if (_hasTransform)
            {
                binaryWriter.Write(_transformMatrix.M11);
                binaryWriter.Write(_transformMatrix.M12);
                binaryWriter.Write(_transformMatrix.M21);
                binaryWriter.Write(_transformMatrix.M22);
            }

            binaryWriter.Write(_ifShowCenterLine);
        }

        /// <summary>
        /// 装载数据
        /// </summary>
        /// <param name="binaryReader"></param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            int verNum = binaryReader.ReadInt32();

            //图元共有参数
            PubLoad(binaryReader);

            if (verNum == 0)
            {
                // 版本0：包含radius参数
                _width = binaryReader.ReadDouble();
                _height = binaryReader.ReadDouble();
                _thickness = binaryReader.ReadDouble();
                _hoAngle = binaryReader.ReadDouble();
                _ifHatch = binaryReader.ReadBoolean();
                _leftOffset = binaryReader.ReadDouble();
                _rightOffset = binaryReader.ReadDouble();
                _topOffset = binaryReader.ReadDouble();
                _bottomOffset = binaryReader.ReadDouble();

                // 加载变换矩阵
                _hasTransform = binaryReader.ReadBoolean();
                if (_hasTransform)
                {
                    double m11 = binaryReader.ReadDouble();
                    double m12 = binaryReader.ReadDouble();
                    double m21 = binaryReader.ReadDouble();
                    double m22 = binaryReader.ReadDouble();
                    _transformMatrix = new Matrix3D(
                        m11, m12, 0, 0,
                        m21, m22, 0, 0,
                        0, 0, 1, 0,
                        0, 0, 0, 1
                    );
                }
                else
                {
                    _transformMatrix = Matrix3D.Identity;
                }
            }

            // 数据加载后需要重新计算几何中心
            _needRecalcCenter = true;
            _isGeometryOperation = false; // 数据加载不是几何操作
            _ifShowCenterLine = binaryReader.ReadBoolean();
        }

        /// <summary>
        /// 深度复制
        /// </summary>
        public override DbElement EleCopy(bool changeUid = false)
        {
            SteelTube ele = new SteelTube();

            // 图元共有参数
            PubCopy(ele);

            // 图元特有参数
            ele._width = _width;
            ele._height = _height;
            ele._thickness = _thickness;
            ele._hoAngle = _hoAngle;
            ele._ifHatch = _ifHatch;
            ele._leftOffset = _leftOffset;
            ele._rightOffset = _rightOffset;
            ele._topOffset = _topOffset;
            ele._bottomOffset = _bottomOffset;

            // 复制变换矩阵
            ele._hasTransform = _hasTransform;
            ele._transformMatrix = _transformMatrix;

            // 操作状态不复制，确保每个图元都有独立的状态
            ele._isGeometryOperation = false; // 复制后不是几何操作

            ele._ifShowCenterLine = _ifShowCenterLine;

            if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }

            return ele;
        }

        #endregion
        #endregion


}
