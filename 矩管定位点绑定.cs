using System;
using System.Collections.Generic;
using System.Windows.Media.Media3D;

/// <summary>
/// 矩管-定位点绑定控制器
/// 实现矩管图元与定位点之间的绑定关系管理和联动更新逻辑
/// 核心功能：插入点控制、拉伸方向判断、变换同步、智能偏移管理
/// </summary>
public class TubeAnchorBinding
{
    #region 绑定类型枚举
    /// <summary>
    /// 绑定类型
    /// </summary>
    public enum BindingType
    {
        /// <summary>
        /// 插入点绑定 - 定位点控制矩管的插入位置
        /// </summary>
        InsertPoint = 0,
        
        /// <summary>
        /// 拉伸控制绑定 - 定位点控制矩管的拉伸方向
        /// </summary>
        StretchControl = 1,
        
        /// <summary>
        /// 对齐参考绑定 - 定位点作为矩管的对齐参考
        /// </summary>
        AlignReference = 2
    }

    /// <summary>
    /// 定位点在矩管中的相对位置
    /// </summary>
    public enum RelativePosition
    {
        /// <summary>
        /// 中心位置
        /// </summary>
        Center = 0,
        
        /// <summary>
        /// 左侧中点
        /// </summary>
        LeftCenter = 1,
        
        /// <summary>
        /// 右侧中点
        /// </summary>
        RightCenter = 2,
        
        /// <summary>
        /// 上侧中点
        /// </summary>
        TopCenter = 3,
        
        /// <summary>
        /// 下侧中点
        /// </summary>
        BottomCenter = 4,
        
        /// <summary>
        /// 左下角
        /// </summary>
        BottomLeft = 5,
        
        /// <summary>
        /// 右下角
        /// </summary>
        BottomRight = 6,
        
        /// <summary>
        /// 左上角
        /// </summary>
        TopLeft = 7,
        
        /// <summary>
        /// 右上角
        /// </summary>
        TopRight = 8,
        
        /// <summary>
        /// 自定义偏移
        /// </summary>
        CustomOffset = 9
    }
    #endregion

    #region 属性
    /// <summary>
    /// 绑定的矩管图元
    /// </summary>
    public SteelTube BoundTube { get; private set; }

    /// <summary>
    /// 绑定的定位点
    /// </summary>
    public AnchorPoint BoundAnchor { get; private set; }

    /// <summary>
    /// 绑定类型
    /// </summary>
    public BindingType Type { get; private set; }

    /// <summary>
    /// 定位点在矩管中的相对位置
    /// </summary>
    public RelativePosition Position { get; set; }

    /// <summary>
    /// 自定义偏移量（当Position为CustomOffset时使用）
    /// </summary>
    public DbPt CustomOffset { get; set; }

    /// <summary>
    /// 是否启用智能拉伸（根据定位点位置自动判断拉伸方向）
    /// </summary>
    public bool EnableSmartStretching { get; set; }

    /// <summary>
    /// 是否启用自动同步（定位点移动时自动更新矩管）
    /// </summary>
    public bool EnableAutoSync { get; set; }

    /// <summary>
    /// 绑定是否活跃
    /// </summary>
    public bool IsActive { get; private set; }
    #endregion

    #region 内部状态
    /// <summary>
    /// 初始偏移量（绑定时记录的偏移关系）
    /// </summary>
    private DbPt _initialOffset;

    /// <summary>
    /// 初始矩管尺寸（用于智能拉伸计算）
    /// </summary>
    private DbPt _initialTubeSize;

    /// <summary>
    /// 全局同步操作跟踪器（防止循环更新）
    /// </summary>
    private static readonly ThreadLocal<HashSet<string>> _activeUpdates = 
        new ThreadLocal<HashSet<string>>(() => new HashSet<string>());

    /// <summary>
    /// 当前绑定的唯一标识符
    /// </summary>
    private readonly string _bindingId;

    /// <summary>
    /// 错误重试计数器
    /// </summary>
    private int _errorRetryCount = 0;

    /// <summary>
    /// 最大重试次数
    /// </summary>
    private const int MAX_RETRY_COUNT = 3;

    /// <summary>
    /// 缓存的变换信息
    /// </summary>
    private CachedTransform _cachedTransform;

    /// <summary>
    /// 变换缓存结构
    /// </summary>
    private struct CachedTransform
    {
        public Matrix3D TransformMatrix;
        public double Angle;
        public DbPt CenterPosition;
        public bool IsValid;
        public DateTime LastUpdate;
        
        public bool IsExpired(TimeSpan maxAge)
        {
            return !IsValid || DateTime.Now - LastUpdate > maxAge;
        }
    }
    #endregion

    #region 构造函数
    /// <summary>
    /// 创建绑定关系
    /// </summary>
    /// <param name="tube">矩管图元</param>
    /// <param name="anchor">定位点</param>
    /// <param name="bindingType">绑定类型</param>
    /// <param name="relativePosition">相对位置</param>
    public TubeAnchorBinding(SteelTube tube, AnchorPoint anchor, BindingType bindingType, 
                            RelativePosition relativePosition = RelativePosition.BottomCenter)
    {
        BoundTube = tube ?? throw new ArgumentNullException(nameof(tube));
        BoundAnchor = anchor ?? throw new ArgumentNullException(nameof(anchor));
        Type = bindingType;
        Position = relativePosition;
        
        // 生成唯一的绑定标识符
        _bindingId = $"{tube.UniqueId}_{anchor.UniqueId}_{bindingType}";
        
        EnableSmartStretching = true;
        EnableAutoSync = true;
        CustomOffset = new DbPt();
        
        // 记录初始状态
        _initialTubeSize = new DbPt(tube.Width, tube.Height);
        
        // 初始化变换缓存
        _cachedTransform = new CachedTransform
        {
            IsValid = false,
            LastUpdate = DateTime.Now
        };
        
        try
        {
            // 计算初始偏移关系
            CalculateInitialOffset();
            
            // 建立绑定关系
            EstablishBinding();
            
            IsActive = true;
        }
        catch (Exception ex)
        {
            IsActive = false;
            LogError($"Failed to create binding: {ex.Message}", ex);
            throw new InvalidOperationException($"Failed to create binding between tube {tube.UniqueId} and anchor {anchor.UniqueId}", ex);
        }
    }
    #endregion

    #region 绑定管理
    /// <summary>
    /// 建立绑定关系
    /// </summary>
    private void EstablishBinding()
    {
        // 在定位点管理器中注册绑定关系
        AnchorPointManager.Instance.BindElementToAnchor(BoundTube.UniqueId, BoundAnchor.UniqueId);
        
        // 根据绑定类型执行初始化操作
        switch (Type)
        {
            case BindingType.InsertPoint:
                SyncTubeToAnchor();
                break;
            case BindingType.StretchControl:
                // 拉伸控制类型不需要初始同步
                break;
            case BindingType.AlignReference:
                // 对齐参考类型暂时不实现
                break;
        }
    }

    /// <summary>
    /// 解除绑定关系
    /// </summary>
    public void UnbindElements()
    {
        if (!IsActive) return;

        // 在定位点管理器中移除绑定关系
        AnchorPointManager.Instance.UnbindElementFromAnchor(BoundTube.UniqueId, BoundAnchor.UniqueId);
        
        IsActive = false;
    }

    /// <summary>
    /// 计算初始偏移关系
    /// </summary>
    private void CalculateInitialOffset()
    {
        DbPt tubeReferencePoint = GetTubeReferencePoint();
        _initialOffset = new DbPt(
            BoundAnchor.Position.X - tubeReferencePoint.X,
            BoundAnchor.Position.Y - tubeReferencePoint.Y
        );
    }

    /// <summary>
    /// 获取矩管的参考点（根据相对位置）
    /// </summary>
    /// <returns>参考点坐标</returns>
    private DbPt GetTubeReferencePoint()
    {
        DbPt centerPt = BoundTube._centerPt;
        double halfWidth = BoundTube.Width / 2.0;
        double halfHeight = BoundTube.Height / 2.0;

        switch (Position)
        {
            case RelativePosition.Center:
                return centerPt.EleCopy();
            case RelativePosition.LeftCenter:
                return new DbPt(centerPt.X - halfWidth, centerPt.Y);
            case RelativePosition.RightCenter:
                return new DbPt(centerPt.X + halfWidth, centerPt.Y);
            case RelativePosition.TopCenter:
                return new DbPt(centerPt.X, centerPt.Y + halfHeight);
            case RelativePosition.BottomCenter:
                return new DbPt(centerPt.X, centerPt.Y - halfHeight);
            case RelativePosition.BottomLeft:
                return new DbPt(centerPt.X - halfWidth, centerPt.Y - halfHeight);
            case RelativePosition.BottomRight:
                return new DbPt(centerPt.X + halfWidth, centerPt.Y - halfHeight);
            case RelativePosition.TopLeft:
                return new DbPt(centerPt.X - halfWidth, centerPt.Y + halfHeight);
            case RelativePosition.TopRight:
                return new DbPt(centerPt.X + halfWidth, centerPt.Y + halfHeight);
            case RelativePosition.CustomOffset:
                return new DbPt(centerPt.X + CustomOffset.X, centerPt.Y + CustomOffset.Y);
            default:
                return centerPt.EleCopy();
        }
    }
    #endregion

    #region 同步更新逻辑
    /// <summary>
    /// 定位点移动时同步更新矩管（增强版：循环检测+错误处理）
    /// </summary>
    public void SyncTubeToAnchor()
    {
        if (!IsActive || !EnableAutoSync) return;
        
        // 检查循环更新
        if (_activeUpdates.Value.Contains(_bindingId))
        {
            LogWarning($"Detected circular update for binding {_bindingId}, skipping sync");
            return;
        }
        
        // 验证绑定对象的有效性
        if (!ValidateBindingObjects())
        {
            LogError("Binding objects are invalid, deactivating binding");
            IsActive = false;
            return;
        }

        _activeUpdates.Value.Add(_bindingId);
        try
        {
            ExecuteWithRetry(() =>
            {
                switch (Type)
                {
                    case BindingType.InsertPoint:
                        SyncTubeInsertPoint();
                        break;
                    case BindingType.StretchControl:
                        SyncTubeStretching();
                        break;
                    case BindingType.AlignReference:
                        SyncTubeAlignment();
                        break;
                    default:
                        throw new InvalidOperationException($"Unknown binding type: {Type}");
                }
            });
            
            // 重置错误计数器
            _errorRetryCount = 0;
        }
        catch (Exception ex)
        {
            LogError($"Failed to sync tube to anchor after {MAX_RETRY_COUNT} retries: {ex.Message}", ex);
            
            // 如果重试次数过多，考虑禁用自动同步
            if (_errorRetryCount >= MAX_RETRY_COUNT)
            {
                EnableAutoSync = false;
                LogError($"Disabling auto sync for binding {_bindingId} due to repeated failures");
            }
        }
        finally
        {
            _activeUpdates.Value.Remove(_bindingId);
        }
    }

    /// <summary>
    /// 矩管变换时同步更新定位点（增强版：循环检测+缓存优化）
    /// </summary>
    public void SyncAnchorToTube()
    {
        if (!IsActive || !EnableAutoSync) return;
        
        // 检查循环更新
        string reverseBindingId = $"{BoundAnchor.UniqueId}_{BoundTube.UniqueId}_reverse";
        if (_activeUpdates.Value.Contains(reverseBindingId))
        {
            LogWarning($"Detected circular update for reverse binding {reverseBindingId}, skipping sync");
            return;
        }
        
        // 验证绑定对象的有效性
        if (!ValidateBindingObjects())
        {
            LogError("Binding objects are invalid, deactivating binding");
            IsActive = false;
            return;
        }

        _activeUpdates.Value.Add(reverseBindingId);
        try
        {
            ExecuteWithRetry(() =>
            {
                // 使用缓存的变换信息优化计算
                DbPt newAnchorPosition = CalculateNewAnchorPositionOptimized();
                
                // 检查位置变化的有效性
                if (IsValidPositionChange(BoundAnchor.Position, newAnchorPosition))
                {
                    BoundAnchor.Position = newAnchorPosition;
                }
                else
                {
                    LogWarning($"Invalid position change detected, skipping anchor update");
                }
            });
            
            // 重置错误计数器
            _errorRetryCount = 0;
        }
        catch (Exception ex)
        {
            LogError($"Failed to sync anchor to tube: {ex.Message}", ex);
        }
        finally
        {
            _activeUpdates.Value.Remove(reverseBindingId);
        }
    }

    /// <summary>
    /// 同步矩管插入点
    /// </summary>
    private void SyncTubeInsertPoint()
    {
        // 根据定位点位置和偏移关系计算新的插入点
        DbPt targetReferencePoint = new DbPt(
            BoundAnchor.Position.X - _initialOffset.X,
            BoundAnchor.Position.Y - _initialOffset.Y
        );

        // 根据相对位置计算新的插入点
        DbPt newInsertPoint = CalculateInsertPointFromReference(targetReferencePoint);
        
        // 更新矩管插入点
        DbPt currentInsert = BoundTube._insertPt;
        double deltaX = newInsertPoint.X - currentInsert.X;
        double deltaY = newInsertPoint.Y - currentInsert.Y;
        
        if (Math.Abs(deltaX) > 0.001 || Math.Abs(deltaY) > 0.001)
        {
            BoundTube.EleMove(deltaX, deltaY);
        }
    }

    /// <summary>
    /// 同步矩管拉伸
    /// </summary>
    private void SyncTubeStretching()
    {
        if (!EnableSmartStretching) return;

        // 计算拉伸方向和距离
        DbPt currentRef = GetTubeReferencePoint();
        DbPt targetRef = new DbPt(
            BoundAnchor.Position.X - _initialOffset.X,
            BoundAnchor.Position.Y - _initialOffset.Y
        );

        double deltaX = targetRef.X - currentRef.X;
        double deltaY = targetRef.Y - currentRef.Y;

        // 根据定位点位置判断拉伸方向
        bool shouldStretchWidth = false;
        bool shouldStretchHeight = false;
        
        switch (Position)
        {
            case RelativePosition.LeftCenter:
            case RelativePosition.RightCenter:
                shouldStretchWidth = true;
                break;
            case RelativePosition.TopCenter:
            case RelativePosition.BottomCenter:
                shouldStretchHeight = true;
                break;
            case RelativePosition.BottomLeft:
            case RelativePosition.BottomRight:
            case RelativePosition.TopLeft:
            case RelativePosition.TopRight:
                shouldStretchWidth = true;
                shouldStretchHeight = true;
                break;
        }

        // 执行拉伸
        if (shouldStretchWidth && Math.Abs(deltaX) > 0.001)
        {
            double newWidth = Math.Max(10, BoundTube.Width + Math.Abs(deltaX) * 2);
            BoundTube.Width = newWidth;
        }

        if (shouldStretchHeight && Math.Abs(deltaY) > 0.001)
        {
            double newHeight = Math.Max(10, BoundTube.Height + Math.Abs(deltaY) * 2);
            BoundTube.Height = newHeight;
        }
    }

    /// <summary>
    /// 同步矩管对齐
    /// </summary>
    private void SyncTubeAlignment()
    {
        // 对齐逻辑暂时不实现，预留接口
    }

    /// <summary>
    /// 根据参考点计算插入点
    /// </summary>
    /// <param name="referencePoint">参考点</param>
    /// <returns>插入点坐标</returns>
    private DbPt CalculateInsertPointFromReference(DbPt referencePoint)
    {
        double halfWidth = BoundTube.Width / 2.0;
        double halfHeight = BoundTube.Height / 2.0;

        switch (Position)
        {
            case RelativePosition.Center:
                return referencePoint.EleCopy();
            case RelativePosition.LeftCenter:
                return new DbPt(referencePoint.X + halfWidth, referencePoint.Y);
            case RelativePosition.RightCenter:
                return new DbPt(referencePoint.X - halfWidth, referencePoint.Y);
            case RelativePosition.TopCenter:
                return new DbPt(referencePoint.X, referencePoint.Y - halfHeight);
            case RelativePosition.BottomCenter:
                return new DbPt(referencePoint.X, referencePoint.Y + halfHeight);
            case RelativePosition.BottomLeft:
                return new DbPt(referencePoint.X + halfWidth, referencePoint.Y + halfHeight);
            case RelativePosition.BottomRight:
                return new DbPt(referencePoint.X - halfWidth, referencePoint.Y + halfHeight);
            case RelativePosition.TopLeft:
                return new DbPt(referencePoint.X + halfWidth, referencePoint.Y - halfHeight);
            case RelativePosition.TopRight:
                return new DbPt(referencePoint.X - halfWidth, referencePoint.Y - halfHeight);
            case RelativePosition.CustomOffset:
                return new DbPt(referencePoint.X - CustomOffset.X, referencePoint.Y - CustomOffset.Y);
            default:
                return referencePoint.EleCopy();
        }
    }

    /// <summary>
    /// 计算定位点的新位置（矩管变换后）
    /// </summary>
    /// <returns>新的定位点位置</returns>
    private DbPt CalculateNewAnchorPosition()
    {
        DbPt referencePoint = GetTubeReferencePoint();
        return new DbPt(
            referencePoint.X + _initialOffset.X,
            referencePoint.Y + _initialOffset.Y
        );
    }
    #endregion

    #region 智能拉伸逻辑
    /// <summary>
    /// 判断定位点相对于矩管几何中心的方位
    /// </summary>
    /// <returns>方位信息</returns>
    public string GetAnchorDirectionFromCenter()
    {
        DbPt center = BoundTube._centerPt;
        DbPt anchor = BoundAnchor.Position;
        
        double deltaX = anchor.X - center.X;
        double deltaY = anchor.Y - center.Y;
        
        string horizontal = Math.Abs(deltaX) < 0.001 ? "Center" : (deltaX > 0 ? "Right" : "Left");
        string vertical = Math.Abs(deltaY) < 0.001 ? "Center" : (deltaY > 0 ? "Top" : "Bottom");
        
        if (horizontal == "Center" && vertical == "Center")
            return "Center";
        else if (horizontal == "Center")
            return vertical;
        else if (vertical == "Center")
            return horizontal;
        else
            return vertical + horizontal;
    }

    /// <summary>
    /// 判断拉伸方向是否远离定位点
    /// </summary>
    /// <param name="stretchDirection">拉伸方向向量</param>
    /// <returns>true表示远离，false表示靠近</returns>
    public bool IsStretchingAwayFromAnchor(DbPt stretchDirection)
    {
        DbPt centerToAnchor = new DbPt(
            BoundAnchor.Position.X - BoundTube._centerPt.X,
            BoundAnchor.Position.Y - BoundTube._centerPt.Y
        );
        
        // 计算点积，大于0表示方向一致（远离）
        double dotProduct = centerToAnchor.X * stretchDirection.X + centerToAnchor.Y * stretchDirection.Y;
        return dotProduct > 0;
    }

    /// <summary>
    /// 获取智能拉伸建议
    /// </summary>
    /// <param name="mousePosition">鼠标位置</param>
    /// <returns>拉伸建议</returns>
    public StretchSuggestion GetStretchSuggestion(DbPt mousePosition)
    {
        DbPt center = BoundTube._centerPt;
        DbPt anchor = BoundAnchor.Position;
        
        // 计算鼠标相对于几何中心的方向
        DbPt mouseDirection = new DbPt(mousePosition.X - center.X, mousePosition.Y - center.Y);
        
        // 计算定位点相对于几何中心的方向
        DbPt anchorDirection = new DbPt(anchor.X - center.X, anchor.Y - center.Y);
        
        StretchSuggestion suggestion = new StretchSuggestion();
        
        // 判断X方向
        if (Math.Abs(mouseDirection.X) > Math.Abs(mouseDirection.Y))
        {
            // 主要是水平方向的拉伸
            if ((mouseDirection.X * anchorDirection.X) > 0)
            {
                suggestion.AllowHorizontal = true;
                suggestion.HorizontalDirection = mouseDirection.X > 0 ? "Right" : "Left";
            }
            else
            {
                suggestion.AllowHorizontal = false;
                suggestion.Reason = "拉伸方向靠近定位点，建议锁定";
            }
        }
        else
        {
            // 主要是垂直方向的拉伸
            if ((mouseDirection.Y * anchorDirection.Y) > 0)
            {
                suggestion.AllowVertical = true;
                suggestion.VerticalDirection = mouseDirection.Y > 0 ? "Up" : "Down";
            }
            else
            {
                suggestion.AllowVertical = false;
                suggestion.Reason = "拉伸方向靠近定位点，建议锁定";
            }
        }
        
        return suggestion;
    }
    #endregion

    #region 增强错误处理和验证
    /// <summary>
    /// 验证绑定对象的有效性
    /// </summary>
    /// <returns>true表示有效，false表示无效</returns>
    private bool ValidateBindingObjects()
    {
        try
        {
            // 检查对象是否为null
            if (BoundTube == null || BoundAnchor == null)
            {
                LogError("Bound objects are null");
                return false;
            }
            
            // 检查UniqueId是否有效
            if (string.IsNullOrEmpty(BoundTube.UniqueId) || string.IsNullOrEmpty(BoundAnchor.UniqueId))
            {
                LogError("Object UniqueIds are invalid");
                return false;
            }
            
            // 检查矩管尺寸是否合理
            if (BoundTube.Width <= 0 || BoundTube.Height <= 0 || BoundTube.Thickness <= 0)
            {
                LogError($"Invalid tube dimensions: {BoundTube.Width}x{BoundTube.Height}x{BoundTube.Thickness}");
                return false;
            }
            
            // 检查定位点是否启用
            if (!BoundAnchor.IsEnabled)
            {
                LogWarning("Anchor point is disabled");
                return false;
            }
            
            return true;
        }
        catch (Exception ex)
        {
            LogError($"Error during validation: {ex.Message}", ex);
            return false;
        }
    }
    
    /// <summary>
    /// 验证位置变化的有效性
    /// </summary>
    /// <param name="oldPosition">旧位置</param>
    /// <param name="newPosition">新位置</param>
    /// <returns>true表示变化有效</returns>
    private bool IsValidPositionChange(DbPt oldPosition, DbPt newPosition)
    {
        if (oldPosition == null || newPosition == null)
            return false;
        
        // 检查位置变化是否合理（防止异常大的跳跃）
        double deltaX = Math.Abs(newPosition.X - oldPosition.X);
        double deltaY = Math.Abs(newPosition.Y - oldPosition.Y);
        
        const double MAX_REASONABLE_CHANGE = 10000.0; // 最大合理变化距离
        
        if (deltaX > MAX_REASONABLE_CHANGE || deltaY > MAX_REASONABLE_CHANGE)
        {
            LogWarning($"Position change too large: ({deltaX}, {deltaY})");
            return false;
        }
        
        // 检查NaN和无穷大
        if (double.IsNaN(newPosition.X) || double.IsNaN(newPosition.Y) ||
            double.IsInfinity(newPosition.X) || double.IsInfinity(newPosition.Y))
        {
            LogError("New position contains NaN or Infinity values");
            return false;
        }
        
        return true;
    }
    
    /// <summary>
    /// 重试执行方法
    /// </summary>
    /// <param name="action">要执行的操作</param>
    private void ExecuteWithRetry(Action action)
    {
        int attempt = 0;
        while (attempt < MAX_RETRY_COUNT)
        {
            try
            {
                action();
                return; // 成功执行，退出重试循环
            }
            catch (Exception ex)
            {
                attempt++;
                _errorRetryCount++;
                
                if (attempt >= MAX_RETRY_COUNT)
                {
                    throw; // 重试次数用完，重新抛出异常
                }
                
                LogWarning($"Retry attempt {attempt} for binding {_bindingId}: {ex.Message}");
                
                // 短暂延迟后重试
                System.Threading.Thread.Sleep(10 * attempt); // 渐进式延迟
            }
        }
    }
    
    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="exception">异常对象（可选）</param>
    private void LogError(string message, Exception exception = null)
    {
        string fullMessage = $"[TubeAnchorBinding {_bindingId}] ERROR: {message}";
        if (exception != null)
        {
            fullMessage += $" - {exception}";
        }
        
        // 这里应该使用实际的日志系统，如NLog、log4net等
        // 现在暂时使用控制台输出
        Console.WriteLine(fullMessage);
        System.Diagnostics.Debug.WriteLine(fullMessage);
    }
    
    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">警告消息</param>
    private void LogWarning(string message)
    {
        string fullMessage = $"[TubeAnchorBinding {_bindingId}] WARNING: {message}";
        
        // 这里应该使用实际的日志系统
        Console.WriteLine(fullMessage);
        System.Diagnostics.Debug.WriteLine(fullMessage);
    }
    
    /// <summary>
    /// 计算定位点的新位置（优化版本：使用缓存的变换信息）
    /// </summary>
    /// <returns>新的定位点位置</returns>
    private DbPt CalculateNewAnchorPositionOptimized()
    {
        // 检查缓存是否过期（1秒过期时间）
        if (_cachedTransform.IsExpired(TimeSpan.FromSeconds(1)))
        {
            // 更新缓存
            UpdateTransformCache();
        }
        
        // 使用缓存的信息计算位置
        DbPt referencePoint = GetTubeReferencePointOptimized();
        return new DbPt(
            referencePoint.X + _initialOffset.X,
            referencePoint.Y + _initialOffset.Y
        );
    }
    
    /// <summary>
    /// 更新变换缓存
    /// </summary>
    private void UpdateTransformCache()
    {
        try
        {
            _cachedTransform = new CachedTransform
            {
                TransformMatrix = BoundTube._hasTransform ? BoundTube._transformMatrix : Matrix3D.Identity,
                Angle = BoundTube.HoAngle,
                CenterPosition = BoundTube._centerPt.EleCopy(),
                IsValid = true,
                LastUpdate = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            LogError($"Failed to update transform cache: {ex.Message}", ex);
            _cachedTransform.IsValid = false;
        }
    }
    
    /// <summary>
    /// 获取矩管的参考点（优化版本：使用缓存）
    /// </summary>
    /// <returns>参考点坐标</returns>
    private DbPt GetTubeReferencePointOptimized()
    {
        if (!_cachedTransform.IsValid)
        {
            // 回退到原始方法
            return GetTubeReferencePoint();
        }
        
        DbPt centerPt = _cachedTransform.CenterPosition;
        double halfWidth = BoundTube.Width / 2.0;
        double halfHeight = BoundTube.Height / 2.0;

        DbPt result;
        switch (Position)
        {
            case RelativePosition.Center:
                result = centerPt.EleCopy();
                break;
            case RelativePosition.LeftCenter:
                result = new DbPt(centerPt.X - halfWidth, centerPt.Y);
                break;
            case RelativePosition.RightCenter:
                result = new DbPt(centerPt.X + halfWidth, centerPt.Y);
                break;
            case RelativePosition.TopCenter:
                result = new DbPt(centerPt.X, centerPt.Y + halfHeight);
                break;
            case RelativePosition.BottomCenter:
                result = new DbPt(centerPt.X, centerPt.Y - halfHeight);
                break;
            case RelativePosition.BottomLeft:
                result = new DbPt(centerPt.X - halfWidth, centerPt.Y - halfHeight);
                break;
            case RelativePosition.BottomRight:
                result = new DbPt(centerPt.X + halfWidth, centerPt.Y - halfHeight);
                break;
            case RelativePosition.TopLeft:
                result = new DbPt(centerPt.X - halfWidth, centerPt.Y + halfHeight);
                break;
            case RelativePosition.TopRight:
                result = new DbPt(centerPt.X + halfWidth, centerPt.Y + halfHeight);
                break;
            case RelativePosition.CustomOffset:
                result = new DbPt(centerPt.X + CustomOffset.X, centerPt.Y + CustomOffset.Y);
                break;
            default:
                result = centerPt.EleCopy();
                break;
        }
        
        // 应用缓存的变换
        if (_cachedTransform.IsValid && Math.Abs(_cachedTransform.Angle) > 0.001)
        {
            double angleRad = _cachedTransform.Angle * Math.PI / 180.0;
            result.RotateSelf(centerPt, angleRad);
        }
        
        return result;
    }
    #endregion

    #region 工具类
    /// <summary>
    /// 拉伸建议结构
    /// </summary>
    public class StretchSuggestion
    {
        public bool AllowHorizontal { get; set; } = false;
        public bool AllowVertical { get; set; } = false;
        public string HorizontalDirection { get; set; } = "";
        public string VerticalDirection { get; set; } = "";
        public string Reason { get; set; } = "";
    }
    #endregion

    #region 静态工厂方法
    /// <summary>
    /// 创建插入点绑定
    /// </summary>
    /// <param name="tube">矩管</param>
    /// <param name="anchor">定位点</param>
    /// <param name="position">相对位置</param>
    /// <returns>绑定实例</returns>
    public static TubeAnchorBinding CreateInsertBinding(SteelTube tube, AnchorPoint anchor, 
                                                       RelativePosition position = RelativePosition.BottomCenter)
    {
        return new TubeAnchorBinding(tube, anchor, BindingType.InsertPoint, position);
    }

    /// <summary>
    /// 创建拉伸控制绑定
    /// </summary>
    /// <param name="tube">矩管</param>
    /// <param name="anchor">定位点</param>
    /// <param name="position">相对位置</param>
    /// <returns>绑定实例</returns>
    public static TubeAnchorBinding CreateStretchBinding(SteelTube tube, AnchorPoint anchor, 
                                                        RelativePosition position = RelativePosition.BottomCenter)
    {
        return new TubeAnchorBinding(tube, anchor, BindingType.StretchControl, position);
    }

    /// <summary>
    /// 创建对齐参考绑定
    /// </summary>
    /// <param name="tube">矩管</param>
    /// <param name="anchor">定位点</param>
    /// <param name="position">相对位置</param>
    /// <returns>绑定实例</returns>
    public static TubeAnchorBinding CreateAlignBinding(SteelTube tube, AnchorPoint anchor, 
                                                      RelativePosition position = RelativePosition.BottomCenter)
    {
        return new TubeAnchorBinding(tube, anchor, BindingType.AlignReference, position);
    }
    #region 静态方法扩展
    /// <summary>
    /// 根据图元ID获取绑定关系
    /// </summary>
    /// <param name="elementId">图元ID</param>
    /// <returns>绑定关系列表</returns>
    public static List<TubeAnchorBinding> GetBindingsByElement(string elementId)
    {
        // 注意：这里需要一个全局的绑定管理器来维护所有绑定关系
        // 暂时返回空列表，实际实现需要配合绑定管理器
        return new List<TubeAnchorBinding>();
    }
    
    /// <summary>
    /// 根据定位点ID获取绑定关系
    /// </summary>
    /// <param name="anchorId">定位点ID</param>
    /// <returns>绑定关系列表</returns>
    public static List<TubeAnchorBinding> GetBindingsByAnchor(string anchorId)
    {
        // 注意：这里需要一个全局的绑定管理器来维护所有绑定关系
        // 暂时返回空列表，实际实现需要配合绑定管理器
        return new List<TubeAnchorBinding>();
    }
    
    /// <summary>
    /// 清理过期的绑定关系
    /// </summary>
    public static void CleanupInactiveBindings()
    {
        // 注意：这里需要配合全局绑定管理器实现
        // 清理所有IsActive为false的绑定关系
    }
    
    /// <summary>
    /// 清理线程本地的更新跟踪器
    /// </summary>
    public static void CleanupThreadLocalUpdates()
    {
        if (_activeUpdates.IsValueCreated)
        {
            _activeUpdates.Value.Clear();
        }
    }
    #endregion
}