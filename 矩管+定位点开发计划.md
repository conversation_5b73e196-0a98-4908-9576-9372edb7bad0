from pathlib import Path

# 文档内容
doc_content = """
# 🏗️ 矩管-定位点组合图元系统开发文档

## 一、项目目标与核心概念

### ✅ 项目目标
开发一个在自研 CAD 平台中运行的复合图元系统，由“钢矩管图元”和“定位点图元”组合而成，实现以下核心功能：
- 支持基点插入、旋转、圆角、拉伸；
- 定位点可控制矩管插入位置、拉伸方向；
- 支持绑定关系，定位点移动时矩管自动调整；
- 所有几何逻辑支持旋转与镜像，保持行为一致性。

## 二、系统构成总览

1. **矩管图元（RectTube）**
   - 参数化：宽度、高度、厚度、圆角半径、旋转角度等；
   - 插入点与几何中心通过偏移控制；
   - 支持动态绘制轮廓、控制点交互。

2. **定位点图元（AnchorPoint）**
   - 表示一个独立的几何锚点；
   - 包含位置、方向、用途等信息；
   - 支持交互拖动和控制行为。

3. **组合控制逻辑（TubeAnchorBinding）**
   - 用于建立矩管与定位点之间的绑定关系；
   - 实现插入点随定位点移动而更新；
   - 支持智能判断定位点在矩管的哪一侧，从而决定拉伸方向。

## 三、开发大步骤与细节拆解

### 🔶 第一步：定义数据模型

- 创建矩管图元模型，包含插入点、几何尺寸、旋转角度等字段；
- 创建定位点图元模型，包含位置、角度、角色标识等字段；
- 创建组合控制类（绑定类），用于管理两者间的偏移关系。

### 🔶 第二步：构建图元控件（WPF 可视化）

- 为矩管创建用户控件，支持按中心点和尺寸绘制；
- 实现旋转支持，使用本地变换进行角度调整；
- 添加控制点（中心点、边中点）并支持可视化拖动；
- 为定位点创建小型可拖动控件，用以表示其位置；
- 保持控件数据与数据模型之间的双向绑定（MVVM 模式推荐）。

### 🔶 第三步：实现矩管几何逻辑

- 从插入点与偏移量计算几何中心；
- 以几何中心为参考生成轮廓矩形；
- 根据圆角半径生成圆角路径；
- 应用旋转变换，支持任意角度绘制；
- 可选：计算内轮廓以表示中空结构。

### 🔶 第四步：设计定位点控制机制

- 定义矩管插入点与定位点之间的初始偏移关系；
- 在定位点移动后，更新矩管的插入点保持偏移恒定；
- 在矩管更新后，可反向推算定位点新位置（双向绑定可选）；
- 提供 UI 选项选择定位点在矩管的哪个参考位置（如底中点、右中点）。

### 🔶 第五步：实现智能拉伸逻辑

- 在控制点拖动时，判断拖动方向相对定位点的远近；
- 若为“远离定位点”，则允许拉伸该方向；
- 若为“靠近定位点”，则锁定该方向，尺寸保持不变；
- 支持同时更新尺寸与插入点的偏移，保持基点稳定；
- 拉伸过程中，实时刷新图形和尺寸显示。

### 🔶 第六步：处理旋转与镜像行为

- 所有定位点、插入点、轮廓点应支持绕插入点进行旋转；
- 镜像操作后，偏移关系应保持一致（如 X/Y 轴取反）；
- 在变换后重新计算偏移方向，确保插入点与定位点关系不变；
- 所有控制逻辑应基于局部坐标系判断方向性。

### 🔶 第七步：交互逻辑集成与用户体验优化

- 支持鼠标选中定位点并拖动；
- 拖动过程中矩管实时联动更新；
- 矩管图元提供悬停高亮、边中点提示、角点控制提示；
- 拖动边中点时根据拉伸规则调整尺寸；
- 插入/创建图元时，支持用户选择插入位置和锚点角色。

## 四、功能扩展建议（后续迭代方向）

| 模块         | 扩展内容 |
|--------------|----------|
| 定位点系统     | 支持多个定位点绑定一个图元，切换当前激活点 |
| 定位点样式     | 定位点根据角色显示不同样式（例如黄色圆点、箭头） |
| 定位吸附     | 拖动定位点时自动吸附最近的线、点、面等构件 |
| 图元组合组     | 支持多个图元与多个定位点组成逻辑组，整体拖动、复制 |
| 属性面板控制   | 在侧边栏实时显示矩管和定位点参数，支持用户修改 |
| 快捷命令支持   | 增加指令：如 `CreateTubeWithAnchor`, `AttachAnchor`, `MirrorTubeGroup` |
| 组行为稳定性   | 拷贝、镜像、旋转组合图元时，自动生成新定位点实例，保持独立控制 |

## 五、部署与测试建议

| 阶段         | 内容 |
|--------------|------|
| 开发验证       | 使用单一窗口测试矩管和定位点行为：插入、旋转、拉伸、镜像 |
| 单元测试       | 编写逻辑层单元测试，验证偏移计算、旋转坐标转换等核心逻辑 |
| 性能评估       | 多组图元同时联动时，测试 UI 响应流畅性 |
| 用户测试       | 通过实际设计师测试交互流，收集建议改进控制点反馈、吸附等交互细节 |
| 插件化部署     | 将矩管-定位点组打包为可复用图元类型，支持跨项目复用 |

## ✅ 结语

该开发框架设计结构清晰、功能可拓展，适合用于实际工程设计中的参数化构件系统。通过定位点作为控制锚点，你可以构建一套非常强大且交互友好的复合图元系统。
"""

# 保存为 Markdown 文件
output_path = Path("/mnt/data/矩管定位点图元系统开发文档.md")
output_path.write_text(doc_content, encoding="utf-8")
output_path

from pathlib import Path

# Markdown 文档内容
md_content = """
# 📌 定位点系统扩展设计文档：统一开关与图元协作机制

## 一、目标说明

为了提升定位点的可控性与平台的扩展性，计划对现有组合图元系统（矩管 + 定位点）做如下升级：

- ✅ **统一控制开关定位点显示/启用状态**；
- ✅ **通过定位点作为锚点插入其他图元**（如铝板、螺栓等）；
- ✅ **定位点支持多图元配合与关系维护**（如自动对齐、自动控制插入方位）；
- ✅ **面向未来的定位点系统，支持类型区分、角色管理、联动规则等**。

---

## 二、系统设计概述

### 🔶 1. 定位点系统能力拆分

| 能力 | 描述 |
|------|------|
| 显示控制 | 可统一打开/关闭所有图元中的定位点（便于清爽视图或交互） |
| 绑定控制 | 可查询图元与其绑定的定位点，并进行解绑、重连等操作 |
| 插入驱动 | 以定位点为锚点快速插入其他图元，且自动吸附对齐 |
| 多角色支持 | 每个定位点可配置角色（如“插入点”、“对齐点”、“控制点”） |
| 联动行为 | 移动定位点时可驱动多个绑定图元联动变换 |

---

## 三、开发步骤建议

### 🧩 步骤 1：定位点状态统一管理

- 引入 `AnchorPointManager` 控制器：
  - 提供统一 API 控制显示与启用状态；
  - 通知所有图元更新定位点可见性。

### 🧩 步骤 2：定位点支持类型标识和属性配置

- 添加 `AnchorType` 枚举（如 Insert、Control、Align）；
- 每种类型显示不同图标/颜色；
- 属性中加入偏移量、锁定方向等高级设置。

### 🧩 步骤 3：以定位点插入其他图元

- 鼠标点击定位点后进入插入模式；
- 插入图元时读取定位点的位置、方向作为锚点；
- 插入图元自动吸附并对齐，初始化相关参数。

### 🧩 步骤 4：支持图元间关系（AnchorGraph）

- 设计结构图维护定位点与图元绑定关系：

移动定位点时通知所有绑定图元联动更新。

🧩 步骤 5：UI 操作体验提升
悬停定位点显示插入菜单（右键浮窗）；

拖动时吸附最近元素；

面板中显示绑定图元与控制属性；

命令支持：/anchor toggle, /anchor insert, /anchor link-to 等。

四、扩展建议
模块	扩展方向
AnchorPointGroup	一个图元多个定位点组合管理
Anchor Snapping	支持图元吸附对齐定位点
逻辑验证	设置主从关系、约束检查
AnchorLayer	区分不同图层或逻辑组管理
插件开放性	图元插入前后处理器支持自定义逻辑

✅ 结语
将“定位点”抽象为具有显示、驱动、插入、绑定等能力的锚点，是构建强大 CAD 参数化平台的核心机制。结合你已有的组合图元架构，该设计将极大提升系统扩展性与交互表现力，为后续“构件化设计”、“构件拼装”、“快速定位建模”等功能打下坚实基础
