using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Windows.Media;
using System.Windows.Media.Media3D;

/// <summary>
/// 定位点图元类 - CAD图元系统中的高级锚点控制器
/// 功能：提供图元间的位置绑定、控制点交互、智能拉伸引导等
/// 支持：移动、旋转、镜像变换，角色类型管理，显示控制
/// 与简化矩管内置定位点系统协同工作，提供更高级的多图元绑定功能
/// </summary>
[Serializable]
[DbElement("定位点", MajorType.Auxiliary)]
public class AnchorPoint : DbElement
{
    #region 定位点类型枚举
    /// <summary>
    /// 定位点角色类型
    /// </summary>
    public enum AnchorType
    {
        /// <summary>
        /// 插入点类型 - 用于控制图元的插入位置
        /// </summary>
        Insert = 0,
        
        /// <summary>
        /// 控制点类型 - 用于控制图元的拉伸变形
        /// </summary>
        Control = 1,
        
        /// <summary>
        /// 对齐点类型 - 用于图元间的对齐吸附
        /// </summary>
        Align = 2,
        
        /// <summary>
        /// 连接点类型 - 用于图元间的连接关系
        /// </summary>
        Connect = 3,
        
        /// <summary>
        /// 矩管专用类型 - 与简化矩管内置定位点关联
        /// </summary>
        TubeSpecific = 4
    }
    
    /// <summary>
    /// 定位点优先级（用于多定位点冲突时的选择）
    /// </summary>
    public enum Priority
    {
        Low = 0,
        Normal = 1, 
        High = 2,
        Critical = 3
    }
    #endregion
    #endregion

    #region 定位点参数
    /// <summary>
    /// 定位点位置
    /// </summary>
    private DbPt _position = new DbPt();
    
    /// <summary>
    /// 定位点位置
    /// </summary>
    [Category("位置参数"), DisplayName("位置"), Description("定位点的位置坐标"), ReadOnly(false)]
    public DbPt Position
    {
        get { return _position; }
        set
        {
            if (_position.Equals(value)) return;
            TransManager.Instance().Push(a => Position = a, _position.EleCopy());
            _position = value.EleCopy();
            
            // 位置改变时通知绑定的图元
            NotifyBindingUpdate();
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 定位点角色类型
    /// </summary>
    private AnchorType _anchorType = AnchorType.Insert;
    
    /// <summary>
    /// 定位点角色类型
    /// </summary>
    [Category("控制参数"), DisplayName("角色类型"), Description("定位点的功能角色"), ReadOnly(false)]
    public AnchorType AnchorType
    {
        get { return _anchorType; }
        set
        {
            if (_anchorType == value) return;
            AnchorType oldType = _anchorType;
            TransManager.Instance().Push(a => AnchorType = a, _anchorType);
            _anchorType = value;
            _needRecalcGeometry = true;
            
            // 触发类型改变事件
            OnTypeChanged?.Invoke(this, _anchorType);
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 定位点优先级
    /// </summary>
    private Priority _priority = Priority.Normal;
    
    /// <summary>
    /// 定位点优先级
    /// </summary>
    [Category("控制参数"), DisplayName("优先级"), Description("定位点的优先级别"), ReadOnly(false)]
    public Priority Priority
    {
        get { return _priority; }
        set
        {
            if (_priority == value) return;
            TransManager.Instance().Push(a => Priority = a, _priority);
            _priority = value;
        }
    }

    /// <summary>
    /// 关联的矩管图元ID（当AnchorType为TubeSpecific时使用）
    /// </summary>
    private string _associatedTubeId = "";
    
    /// <summary>
    /// 关联的矩管图元ID
    /// </summary>
    [Category("绑定信息"), DisplayName("关联矩管"), Description("关联的矩管图元ID"), ReadOnly(true)]
    public string AssociatedTubeId
    {
        get { return _associatedTubeId; }
        set { _associatedTubeId = value ?? ""; }
    }

    /// <summary>
    /// 关联的矩管内置定位点类型
    /// </summary>
    private LocatorPointType _tubeLocatorType = LocatorPointType.GeometryCenter;
    
    /// <summary>
    /// 关联的矩管内置定位点类型
    /// </summary>
    [Category("绑定信息"), DisplayName("矩管定位点类型"), Description("关联的矩管内置定位点类型"), ReadOnly(false)]
    public LocatorPointType TubeLocatorType
    {
        get { return _tubeLocatorType; }
        set
        {
            if (_tubeLocatorType == value) return;
            _tubeLocatorType = value;
            // 如果是矩管专用类型，同步更新关联矩管的定位点
            if (_anchorType == AnchorType.TubeSpecific)
            {
                SyncWithTubeLocator();
            }
        }
    }

    /// <summary>
    /// 方向角度（用于控制拉伸方向等）
    /// </summary>
    private double _direction = 0.0;
    
    /// <summary>
    /// 方向角度
    /// </summary>
    [Category("控制参数"), DisplayName("方向角度"), Description("定位点的方向角度（度）"), ReadOnly(false)]
    public double Direction
    {
        get { return _direction; }
        set
        {
            double normalizedValue = value;
            while (normalizedValue > 180) normalizedValue -= 360;
            while (normalizedValue < -180) normalizedValue += 360;
            
            if (Math.Abs(_direction - normalizedValue) < 0.001) return;
            TransManager.Instance().Push(a => Direction = a, _direction);
            _direction = normalizedValue;
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 定位点显示大小
    /// </summary>
    private double _displaySize = 8.0;
    
    /// <summary>
    /// 定位点显示大小
    /// </summary>
    [Category("显示参数"), DisplayName("显示大小"), Description("定位点的显示大小（像素）"), ReadOnly(false)]
    public double DisplaySize
    {
        get { return _displaySize; }
        set
        {
            if (Math.Abs(_displaySize - value) < 0.001) return;
            TransManager.Instance().Push(a => DisplaySize = a, _displaySize);
            _displaySize = Math.Max(4.0, Math.Min(20.0, value));
            _needRecalcGeometry = true;
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 吸附半径（用于自动吸附其他定位点或图元特征点）
    /// </summary>
    private double _snapRadius = 10.0;
    
    /// <summary>
    /// 吸附半径
    /// </summary>
    [Category("控制参数"), DisplayName("吸附半径"), Description("自动吸附的搜索半径（像素）"), ReadOnly(false)]
    public double SnapRadius
    {
        get { return _snapRadius; }
        set
        {
            if (Math.Abs(_snapRadius - value) < 0.001) return;
            TransManager.Instance().Push(a => SnapRadius = a, _snapRadius);
            _snapRadius = Math.Max(1.0, Math.Min(50.0, value));
        }
    }

    /// <summary>
    /// 是否启用自动吸附
    /// </summary>
    private bool _enableSnap = true;
    
    /// <summary>
    /// 是否启用自动吸附
    /// </summary>
    [Category("控制参数"), DisplayName("启用吸附"), Description("是否启用自动吸附功能"), ReadOnly(false)]
    public bool EnableSnap
    {
        get { return _enableSnap; }
        set
        {
            if (_enableSnap == value) return;
            TransManager.Instance().Push(a => EnableSnap = a, _enableSnap);
            _enableSnap = value;
        }
    }
    
    /// <summary>
    /// 是否启用定位点
    /// </summary>
    private bool _isEnabled = true;
    
    /// <summary>
    /// 是否启用定位点
    /// </summary>
    [Category("控制参数"), DisplayName("启用状态"), Description("定位点是否启用"), ReadOnly(false)]
    public bool IsEnabled
    {
        get { return _isEnabled; }
        set
        {
            if (_isEnabled == value) return;
            TransManager.Instance().Push(a => IsEnabled = a, _isEnabled);
            _isEnabled = value;
            _needRecalcGeometry = true;
            
            // 触发启用状态改变事件
            OnEnabledChanged?.Invoke(this, _isEnabled);
            
            ActCutCal2D3D();
        }
    }

    /// <summary>
    /// 绑定的图元ID列表
    /// </summary>
    private List<string> _boundElementIds = new List<string>();
    
    /// <summary>
    /// 绑定的图元ID列表（只读，通过绑定方法管理）
    /// </summary>
    [Category("绑定信息"), DisplayName("绑定图元"), Description("与此定位点绑定的图元ID列表"), ReadOnly(true)]
    public string BoundElements
    {
        get { return string.Join(", ", _boundElementIds); }
    }
    #endregion

    #region 内部状态
    /// <summary>
    /// 是否需要重新计算几何
    /// </summary>
    private bool _needRecalcGeometry = true;
    
    /// <summary>
    /// 是否为几何操作（移动、旋转、镜像）
    /// </summary>
    private bool _isGeometryOperation = false;
    
    /// <summary>
    /// 变换矩阵
    /// </summary>
    private Matrix3D _transformMatrix = Matrix3D.Identity;
    
    /// <summary>
    /// 是否应用了变换
    /// </summary>
    private bool _hasTransform = false;
    #endregion

    #region 事件系统
    /// <summary>
    /// 定位点位置改变事件
    /// </summary>
    public event Action<AnchorPoint, DbPt> OnPositionChanged;

    /// <summary>
    /// 定位点类型改变事件
    /// </summary>
    public event Action<AnchorPoint, AnchorType> OnTypeChanged;

    /// <summary>
    /// 定位点启用状态改变事件
    /// </summary>
    public event Action<AnchorPoint, bool> OnEnabledChanged;
    #endregion

    #region 构造函数
    /// <summary>
    /// 无参构造函数
    /// </summary>
    public AnchorPoint()
    {
        _if3D = false;
        LayerSet("定位点");
    }

    /// <summary>
    /// 标准构造函数
    /// </summary>
    /// <param name="position">定位点位置</param>
    /// <param name="anchorType">定位点类型</param>
    /// <param name="direction">方向角度</param>
    /// <param name="displaySize">显示大小</param>
    public AnchorPoint(DbPt position, AnchorType anchorType = AnchorType.Insert, 
                       double direction = 0.0, double displaySize = 8.0)
    {
        _if3D = false;
        LayerSet("定位点");
        _position = position.EleCopy();
        _anchorType = anchorType;
        _direction = direction;
        _displaySize = Math.Max(4.0, Math.Min(20.0, displaySize));
        _isEnabled = true;
        _needRecalcGeometry = true;
        _isGeometryOperation = false;
    }
    #endregion

    #region 绑定管理
    /// <summary>
    /// 添加绑定图元
    /// </summary>
    /// <param name="elementId">图元ID</param>
    public void AddBinding(string elementId)
    {
        if (!string.IsNullOrEmpty(elementId) && !_boundElementIds.Contains(elementId))
        {
            _boundElementIds.Add(elementId);
        }
    }

    /// <summary>
    /// 移除绑定图元
    /// </summary>
    /// <param name="elementId">图元ID</param>
    public void RemoveBinding(string elementId)
    {
        _boundElementIds.Remove(elementId);
    }

    /// <summary>
    /// 清除所有绑定
    /// </summary>
    public void ClearBindings()
    {
        _boundElementIds.Clear();
    }

    /// <summary>
    /// 获取绑定的图元ID列表
    /// </summary>
    /// <returns>绑定的图元ID列表</returns>
    public List<string> GetBoundElementIds()
    {
        return new List<string>(_boundElementIds);
    }

    /// <summary>
    /// 通知绑定更新（当定位点位置改变时）
    /// </summary>
    private void NotifyBindingUpdate()
    {
        // 触发绑定图元的更新
        foreach (string elementId in _boundElementIds)
        {
            // 查找绑定关系并触发同步
            var bindings = TubeAnchorBinding.GetBindingsByElement(elementId);
            foreach (var binding in bindings)
            {
                if (binding.BoundAnchor.UniqueId == this.UniqueId)
                {
                    binding.SyncTubeToAnchor();
                }
            }
        }
        
        // 触发位置改变事件
        OnPositionChanged?.Invoke(this, _position);
    }

    /// <summary>
    /// 同步与矩管定位点的关联
    /// </summary>
    private void SyncWithTubeLocator()
    {
        if (_anchorType != AnchorType.TubeSpecific || string.IsNullOrEmpty(_associatedTubeId))
            return;
            
        // 通过管理器查找关联的矩管
        // 注意：这里需要一个通用的图元管理器来获取图元实例
        // 暂时使用AnchorPointManager来间接访问
        var boundElements = AnchorPointManager.Instance.GetAnchorBoundElements(this.UniqueId);
        
        foreach (string elementId in boundElements)
        {
            if (elementId == _associatedTubeId)
            {
                // 找到对应的绑定关系
                var bindings = TubeAnchorBinding.GetBindingsByAnchor(this.UniqueId);
                foreach (var binding in bindings)
                {
                    if (binding.BoundTube.UniqueId == elementId)
                    {
                        // 根据矩管状态计算新的定位点位置
                        DbPt newPosition = binding.CalculateNewAnchorPosition();
                        if (newPosition != null && !newPosition.Equals(_position))
                        {
                            _position = newPosition;
                            _needRecalcGeometry = true;
                            ActCutCal2D3D();
                        }
                        break;
                    }
                }
                break;
            }
        }
    }
    #endregion

    #region 几何计算
    /// <summary>
    /// 计算定位点的控制点
    /// </summary>
    private void CalculateControlPoints()
    {
        ConPts.Clear();
        
        // 添加中心点
        DbPt centerPt = _position.EleCopy();
        centerPt.PtType = 0; // 中心点
        ConPts.Add(centerPt);
        
        // 根据显示大小添加外围控制点（用于显示和交互）
        double halfSize = _displaySize / 2.0;
        
        // 添加四个方向的控制点
        DbPt[] directionPoints = new DbPt[]
        {
            new DbPt(_position.X + halfSize, _position.Y),      // 右
            new DbPt(_position.X, _position.Y + halfSize),      // 上
            new DbPt(_position.X - halfSize, _position.Y),      // 左
            new DbPt(_position.X, _position.Y - halfSize)       // 下
        };

        // 如果有方向角度，旋转控制点
        if (Math.Abs(_direction) > 0.001)
        {
            double angleRad = _direction * Math.PI / 180.0;
            for (int i = 0; i < directionPoints.Length; i++)
            {
                directionPoints[i].RotateSelf(_position, angleRad);
            }
        }

        // 添加控制点
        foreach (DbPt pt in directionPoints)
        {
            pt.PtType = 1; // 外围控制点
            ConPts.Add(pt);
        }

        // 应用变换矩阵（如果有的话）
        if (_hasTransform)
        {
            ApplyTransformMatrix();
        }
    }

    /// <summary>
    /// 应用变换矩阵
    /// </summary>
    private void ApplyTransformMatrix()
    {
        if (!_hasTransform || ConPts.Count <= 1) return;

        DbPt center = ConPts[0]; // 中心点作为变换原点

        // 对除中心点外的所有控制点应用变换
        for (int i = 1; i < ConPts.Count; i++)
        {
            // 转换为相对坐标
            double relativeX = ConPts[i].X - center.X;
            double relativeY = ConPts[i].Y - center.Y;

            // 应用变换矩阵
            double transformedX = _transformMatrix.M11 * relativeX + _transformMatrix.M12 * relativeY;
            double transformedY = _transformMatrix.M21 * relativeX + _transformMatrix.M22 * relativeY;

            // 转换回全局坐标
            ConPts[i].X = center.X + transformedX;
            ConPts[i].Y = center.Y + transformedY;
        }
    }

    /// <summary>
    /// 生成定位点的显示几何
    /// </summary>
    private void GenerateDisplayGeometry()
    {
        Lines.Clear();

        if (!_isEnabled || ConPts.Count < 5) return;

        DbPt center = ConPts[0];
        
        // 根据定位点类型生成不同的显示形状
        switch (_anchorType)
        {
            case AnchorType.Insert:
                GenerateInsertPointGeometry(center);
                break;
            case AnchorType.Control:
                GenerateControlPointGeometry(center);
                break;
            case AnchorType.Align:
                GenerateAlignPointGeometry(center);
                break;
            case AnchorType.Connect:
                GenerateConnectPointGeometry(center);
                break;
        }
    }

    /// <summary>
    /// 生成插入点几何（实心圆）
    /// </summary>
    private void GenerateInsertPointGeometry(DbPt center)
    {
        double radius = _displaySize / 2.0;
        int segments = 16;
        
        List<DbPt> circlePoints = new List<DbPt>();
        for (int i = 0; i < segments; i++)
        {
            double angle = 2 * Math.PI * i / segments;
            DbPt pt = new DbPt(
                center.X + radius * Math.Cos(angle),
                center.Y + radius * Math.Sin(angle)
            );
            circlePoints.Add(pt);
        }

        // 创建圆形线段
        for (int i = 0; i < circlePoints.Count; i++)
        {
            DbPt start = circlePoints[i];
            DbPt end = circlePoints[(i + 1) % circlePoints.Count];
            DbLine line = new DbLine(start, end);
            Lines.Add(line);
        }
    }

    /// <summary>
    /// 生成控制点几何（方形）
    /// </summary>
    private void GenerateControlPointGeometry(DbPt center)
    {
        double halfSize = _displaySize / 2.0;
        
        DbPt[] corners = new DbPt[]
        {
            new DbPt(center.X - halfSize, center.Y - halfSize),
            new DbPt(center.X + halfSize, center.Y - halfSize),
            new DbPt(center.X + halfSize, center.Y + halfSize),
            new DbPt(center.X - halfSize, center.Y + halfSize)
        };

        // 创建方形线段
        for (int i = 0; i < corners.Length; i++)
        {
            DbPt start = corners[i];
            DbPt end = corners[(i + 1) % corners.Length];
            DbLine line = new DbLine(start, end);
            Lines.Add(line);
        }
    }

    /// <summary>
    /// 生成对齐点几何（三角形）
    /// </summary>
    private void GenerateAlignPointGeometry(DbPt center)
    {
        double size = _displaySize;
        double height = size * Math.Sqrt(3) / 2;
        
        DbPt[] corners = new DbPt[]
        {
            new DbPt(center.X, center.Y + height / 2),          // 上顶点
            new DbPt(center.X - size / 2, center.Y - height / 2), // 左下
            new DbPt(center.X + size / 2, center.Y - height / 2)  // 右下
        };

        // 如果有方向角度，旋转三角形
        if (Math.Abs(_direction) > 0.001)
        {
            double angleRad = _direction * Math.PI / 180.0;
            for (int i = 0; i < corners.Length; i++)
            {
                corners[i].RotateSelf(center, angleRad);
            }
        }

        // 创建三角形线段
        for (int i = 0; i < corners.Length; i++)
        {
            DbPt start = corners[i];
            DbPt end = corners[(i + 1) % corners.Length];
            DbLine line = new DbLine(start, end);
            Lines.Add(line);
        }
    }

    /// <summary>
    /// 生成连接点几何（十字形）
    /// </summary>
    private void GenerateConnectPointGeometry(DbPt center)
    {
        double halfSize = _displaySize / 2.0;
        
        // 水平线
        DbLine hLine = new DbLine(
            new DbPt(center.X - halfSize, center.Y),
            new DbPt(center.X + halfSize, center.Y)
        );
        Lines.Add(hLine);
        
        // 垂直线
        DbLine vLine = new DbLine(
            new DbPt(center.X, center.Y - halfSize),
            new DbPt(center.X, center.Y + halfSize)
        );
        Lines.Add(vLine);
    }
    #endregion

    #region 图元操作方法
    /// <summary>
    /// 图元激活计算
    /// </summary>
    public override void Activate()
    {
        Hatchs.Clear();
        Lines.Clear();

        // 计算控制点
        if (_needRecalcGeometry || ConPts.Count == 0)
        {
            CalculateControlPoints();
            _needRecalcGeometry = false;
        }
        else if (_isGeometryOperation && ConPts.Count > 0)
        {
            // 几何操作后更新位置
            _position = ConPts[0].EleCopy();
        }

        // 生成显示几何
        GenerateDisplayGeometry();

        // 设置线段属性
        foreach (DbLine line in Lines)
        {
            line.SetStatus(1, 1, 1);
            line.LayerId = PreLayerManage.GetLayerId("定位点");
        }

        LayerChange(layerManage.GetLayer(_layerId));

        if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
        if (_colorIndex >= 0) { foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; } }
        if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

        EleArea = new DbEleArea(this);
        CalSolid2D();

        // 重置状态
        _isGeometryOperation = false;
    }

    /// <summary>
    /// 图元提示信息
    /// </summary>
    public override void EleTips(out string str1, out string str2)
    {
        str1 = "定位点";
        str2 = $"{_anchorType} ({_position.X:F1},{_position.Y:F1})";
    }

    /// <summary>
    /// 图元移动
    /// </summary>
    public override void EleMove(double X, double Y)
    {
        // 移动所有控制点
        foreach (DbPt pt in ConPts)
        {
            pt.MoveSelf(X, Y);
        }

        // 更新位置
        if (ConPts.Count > 0)
        {
            _position = ConPts[0].EleCopy();
        }

        // 通知绑定更新
        NotifyBindingUpdate();

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 图元拉伸（定位点不支持拉伸，转为移动）
    /// </summary>
    public override void EleMove_s(double X, double Y)
    {
        // 定位点被拖拽时执行移动
        if (ConPts.Count > 0 && ConPts[0].Status == 1)
        {
            EleMove(X, Y);
        }
    }

    /// <summary>
    /// 图元旋转
    /// </summary>
    public override void EleMove_r(DbPt rotCenter, double angle)
    {
        // 旋转所有控制点
        foreach (DbPt pt in ConPts)
        {
            pt.RotateSelf(rotCenter, angle);
        }

        // 更新位置和方向
        if (ConPts.Count > 0)
        {
            _position = ConPts[0].EleCopy();
        }
        
        // 更新方向角度
        _direction += angle * 180 / Math.PI;
        while (_direction > 180) _direction -= 360;
        while (_direction < -180) _direction += 360;

        // 通知绑定更新
        NotifyBindingUpdate();

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 图元镜像
    /// </summary>
    public override void EleMove_m(DbLine mirrorLine)
    {
        // 镜像所有控制点
        foreach (DbPt pt in ConPts)
        {
            pt.MirrorSelf(mirrorLine);
        }

        // 更新位置
        if (ConPts.Count > 0)
        {
            _position = ConPts[0].EleCopy();
        }

        // 更新变换矩阵
        UpdateTransformMatrixForMirror(mirrorLine);

        // 通知绑定更新
        NotifyBindingUpdate();

        _isGeometryOperation = true;
        Activate();
    }

    /// <summary>
    /// 更新镜像变换矩阵
    /// </summary>
    private void UpdateTransformMatrixForMirror(DbLine mirrorLine)
    {
        // 计算镜像线的方向向量
        DbPt lineDir = mirrorLine.PtEnd - mirrorLine.PtSt;
        lineDir.Normalize2D();
        
        // 计算镜像线的法向量
        DbPt normal = new DbPt(-lineDir.Y, lineDir.X);
        
        // 创建镜像矩阵
        double nx = normal.X;
        double ny = normal.Y;
        
        Matrix3D mirrorMatrix = new Matrix3D(
            1 - 2 * nx * nx, -2 * nx * ny, 0, 0,
            -2 * nx * ny, 1 - 2 * ny * ny, 0, 0,
            0, 0, 1, 0,
            0, 0, 0, 1
        );

        // 组合变换矩阵
        _transformMatrix = Matrix3D.Multiply(_transformMatrix, mirrorMatrix);
        _hasTransform = true;
    }
    #endregion

    #region 数据持久化
    /// <summary>
    /// 保存数据
    /// </summary>
    public override void DataSave(BinaryWriter binaryWriter)
    {
        // 图元类标识
        binaryWriter.Write(this.GetType().ToString());

        // 版本号
        binaryWriter.Write(1);

        // 图元共有参数
        PubSave(binaryWriter);

        // 定位点特有参数
        binaryWriter.Write(_position.X);
        binaryWriter.Write(_position.Y);
        binaryWriter.Write(_position.Z);
        binaryWriter.Write((int)_anchorType);
        binaryWriter.Write(_direction);
        binaryWriter.Write(_displaySize);
        binaryWriter.Write(_isEnabled);
        
        // 绑定信息
        binaryWriter.Write(_boundElementIds.Count);
        foreach (string id in _boundElementIds)
        {
            binaryWriter.Write(id ?? "");
        }
        
        // 变换矩阵
        binaryWriter.Write(_hasTransform);
        if (_hasTransform)
        {
            binaryWriter.Write(_transformMatrix.M11);
            binaryWriter.Write(_transformMatrix.M12);
            binaryWriter.Write(_transformMatrix.M21);
            binaryWriter.Write(_transformMatrix.M22);
        }
    }

    /// <summary>
    /// 装载数据
    /// </summary>
    public override void DataLoad(BinaryReader binaryReader)
    {
        int verNum = binaryReader.ReadInt32();

        // 图元共有参数
        PubLoad(binaryReader);

        if (verNum >= 1)
        {
            // 定位点参数
            _position = new DbPt(
                binaryReader.ReadDouble(),
                binaryReader.ReadDouble(),
                binaryReader.ReadDouble()
            );
            _anchorType = (AnchorType)binaryReader.ReadInt32();
            _direction = binaryReader.ReadDouble();
            _displaySize = binaryReader.ReadDouble();
            _isEnabled = binaryReader.ReadBoolean();
            
            // 绑定信息
            int bindingCount = binaryReader.ReadInt32();
            _boundElementIds.Clear();
            for (int i = 0; i < bindingCount; i++)
            {
                string id = binaryReader.ReadString();
                if (!string.IsNullOrEmpty(id))
                {
                    _boundElementIds.Add(id);
                }
            }
            
            // 变换矩阵
            _hasTransform = binaryReader.ReadBoolean();
            if (_hasTransform)
            {
                double m11 = binaryReader.ReadDouble();
                double m12 = binaryReader.ReadDouble();
                double m21 = binaryReader.ReadDouble();
                double m22 = binaryReader.ReadDouble();
                _transformMatrix = new Matrix3D(
                    m11, m12, 0, 0,
                    m21, m22, 0, 0,
                    0, 0, 1, 0,
                    0, 0, 0, 1
                );
            }
            else
            {
                _transformMatrix = Matrix3D.Identity;
            }
        }
        
        _needRecalcGeometry = true;
        _isGeometryOperation = false;
    }

    /// <summary>
    /// 深度复制
    /// </summary>
    public override DbElement EleCopy(bool changeUid = false)
    {
        AnchorPoint ele = new AnchorPoint();

        // 图元共有参数
        PubCopy(ele);

        // 定位点参数
        ele._position = _position.EleCopy();
        ele._anchorType = _anchorType;
        ele._direction = _direction;
        ele._displaySize = _displaySize;
        ele._isEnabled = _isEnabled;
        
        // 绑定信息（复制时不复制绑定关系）
        ele._boundElementIds = new List<string>();
        
        // 变换矩阵
        ele._hasTransform = _hasTransform;
        ele._transformMatrix = _transformMatrix;

        // 状态重置
        ele._needRecalcGeometry = true;
        ele._isGeometryOperation = false;

        if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); }

        return ele;
    }
    #endregion
}