    /// <summary>
    /// 钢矩管-侧式图元
    /// </summary>
    public class SteelTubeSideType : DbElement
    {
        #region 标识数据
        /// <summary>
        /// 是否闭合
        /// </summary>
        private bool _close = false;
        /// <summary>
        /// 是否闭合
        /// </summary>
        [Category("标识数据"), DisplayName("是否闭合"), Description("钢矩管是否闭合"), ReadOnly(false)]
        public bool Closed
        {
            get { return _close; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Closed = a, _close);
                _close = value;
                if (_close)//由非闭合变为闭合
                {

                    int iCt1 = ConPts.Count - 1;

                    //如果控制点列表中最后一个点为普通点
                    if (ConPts[iCt1].PtType == 0)
                    {

                        //倒数第二点是否为弧线中点，若不是连接首尾计算其中点
                        if (ConPts[iCt1 - 1].PtType != 1)
                        {
                            if (ConPts.Count <= 3)
                            {
                                EBDB.Instance.UICmdLine("单段矩管无法闭合", null, null);
                                return;
                            }

                            DbPt pt = 0.5 * (ConPts[iCt1] + ConPts[0]);
                            pt.PtType = 2; //设置类型为普通中点
                            ConPts.Add(pt); //添加到控制点列表中
                        }
                        else
                        {
                            //如果倒数第二点是弧线中点，
                            DbPt ptSt = ConPts[iCt1];
                            DbPt ptEnd = ConPts[0];
                            DbLine arc = new DbLine(ConPts[iCt1 - 2], ptSt, ConPts[iCt1 - 1]);
                            DbLine lineT = GMath.GetArcTangentLine(arc, false);
                            DbPt dirLast = lineT.PtSt - lineT.PtEnd;
                            DbPt dirT = new DbPt(-dirLast.Y, dirLast.X); //垂直方向
                            DbPt pt1 = ptSt + dirT;
                            DbLine line1 = new DbLine(ptSt, pt1);
                            DbLine line2 = new DbLine(ptSt, ptEnd);
                            DbLine line3 = GMath.GetVerLine(line2, line2.PtMid); //垂直平分线
                            DbPt ptC = GMath.LineCrossPt(line1, line3);
                            if (ptC == null)
                            {
                                DbPt pt = 0.5 * (ConPts[iCt1] + ConPts[0]);
                                pt.PtType = 2; //这是为普通中点
                                ConPts.Add(pt); //添加到控制点列表中
                            }
                            else
                            {
                                double rr = GMath.Distance(ptC, ptSt);
                                DbPt dirNew = line3.PtEnd - line3.PtSt; //获取垂直线的方向
                                dirNew.Normalize(); //归一化方向向量
                                double temp = dirLast * dirNew;
                                if (temp < 0) { dirNew = -dirNew; }//计算夹角，大于90°方向相反
                                DbPt ptMid = ptC + rr * dirNew; //计算中点
                                ptMid.PtType = 1; //弧形中点
                                ConPts.Add(ptMid);
                            }
                        }
                    }

                }
                else//闭合变为非闭合
                {
                    int iCt1 = ConPts.Count - 1;
                    if (ConPts[iCt1].PtType == 1 || ConPts[iCt1].PtType == 2)
                    {
                        //如果最后一个点是弧线中点或普通点，则删除
                        ConPts.RemoveAt(iCt1);
                    }
                }

                if (AutoActivate) { ActAndCal2D(); }
            }
        }

        /// <summary>
        /// 方向控制
        /// </summary>
        private bool _direction = true;
        /// <summary>
        /// 方向控制
        /// </summary>
        [Category("标识数据"), DisplayName("内外朝向"), Description("钢矩管朝向"), ReadOnly(false)]
        public bool Direction
        {
            get { return _direction; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Direction = a, _direction);

                _direction = value;

                //对于二维图元，如果参数改变导致形体变化，调用：ActAndCal2D()
                //对于二维图元，如果参数改变不会导致图元形体变化，不需要调用
                //对于三维图元，如果参数改变导致形体变化，调用：ActCutCal2D3D()
                //对于三维图元，如果参数改变不会导致图元形体变化，不需要调用
                ActCutCal2D3D();
            }
        }
        #endregion

        #region 几何参数
        /// <summary>
        /// 高度(截面图中的高度）
        /// </summary>
        private double _height;
        /// <summary>
        /// 高度(截面图中的高度）
        /// </summary>
        [Category("几何参数"), DisplayName("高度"), Description("钢矩管高度"), ReadOnly(false)]
        public double Height
        {
            get { return _height; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Height = a, _height);
                _height = value;
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 宽度(截面图中的宽度）
        /// </summary>
        private double _width;
        /// <summary>
        /// 高度(截面图中的高度）
        /// </summary>
        [Category("几何参数"), DisplayName("宽度"), Description("钢矩管宽度"), ReadOnly(true)]
        public double Width
        {
            get { return _width; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Width = a, _width);
                _width = value;
                
            }
        }

        /// <summary>
        /// 壁厚
        /// </summary>
        private double _thickness;
        /// <summary>
        /// 壁厚
        /// </summary>
        [Category("几何参数"), DisplayName("壁厚"), Description("钢矩管壁厚"), ReadOnly(false)]
        public double Thickness
        {
            get { return _thickness; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Thickness = a, _thickness);
                _thickness = value;
                ActCutCal2D3D();
            }
        }

        ///<summary>
        ///总长度
        ///</summary>
        private double _length = 0;
        ///<summary>
        ///总长度
        ///</summary>
        [Category("几何参数"), DisplayName("总长度"), Description("钢矩管的总长度。"), ReadOnly(true)]
        public double Length { get { return _length; } }

        /// <summary>
        /// 宽度是否随出图比例自适应
        /// </summary>
        private bool _autoScale = false;
        /// <summary>
        /// 宽度是否随出图比例自适应
        /// </summary>
        [Category("几何参数"), DisplayName("出图比例自适应"), Description("铝单板宽度是否随出图比例自适应。"), ReadOnly(false)]
        public bool AutoScale { get { return _autoScale; } set { _autoScale = value; if (AutoActivate) { ActCutCal2D3D(); } } }

        /// <summary>
        /// 起始端封口
        /// </summary>
        private bool _leftEdge = false;
        /// <summary>
        /// 起始端封口
        /// </summary>
        [Category("几何参数"), DisplayName("起始端封口"), Description("钢矩管起始端封口"), ReadOnly(false)]
        public bool LeftEdge
        {
            get { return _leftEdge; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => LeftEdge = a, _leftEdge);
                _leftEdge = value;

                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 终止端封口
        /// </summary>
        private bool _rightEdge = false;
        /// <summary>
        /// 终止端封口
        /// </summary>
        [Category("几何参数"), DisplayName("终止端折边"), Description("钢矩管终止端封口"), ReadOnly(false)]
        public bool RightEdge
        {
            get { return _rightEdge; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => RightEdge = a, _rightEdge);
                _rightEdge = value;

                if (!_close)
                {
                    if (_rightEdge)
                    {

                    }
                }
                else
                {
                    //如果是闭合铝单板，左侧折边无效
                    _rightEdge = false;
                }

                ActCutCal2D3D();
            }
        }

        #endregion

        #region 材料及表面处理
        /// <summary>
        /// 材质
        /// </summary>
        private DbMaterial _material = new DbMaterial("钢 - 耐候钢");
        /// <summary>
        /// 材质
        /// </summary>
        [Category("材料"), DisplayName("构件材料"), Description("当前构件材料"), ReadOnly(false)]
        [Editor(typeof(Btn_MatSelect), typeof(Btn_MatSelect))]
        public DbMaterial Material
        {
            get => _material;
            set
            {
                if (_material == value) return;
                TransManager.Instance().Push(a => Material = a, _material);
                _material = value;
                if (AutoActivate)
                {
                    ActCutCal2D3D();
                }
                //EBDB.Instance.GProject.GetAllEle3DSet();
            }

        }

        /// <summary>
        /// 表面处理方式
        /// </summary>
        private SurfaceTreatmentType _surfaceTreatment = new SurfaceTreatmentType();
        /// <summary>
        /// 表面处理方式
        /// </summary>
        [Category("表面处理方式"), DisplayName("表面处理方式"), Description("表面处理方式"), ReadOnly(false)]
        [Editor(typeof(EnumEditor), typeof(EnumEditor))]
        public SurfaceTreatmentType SurfaceTreatment
        {
            get => _surfaceTreatment;
            set
            {
                if (_surfaceTreatment == value) return;
                TransManager.Instance().Push(a => SurfaceTreatment = a, _surfaceTreatment);
                _surfaceTreatment = value;
                if (AutoActivate) { ActCutCal2D3D(); }
            }
        }

        #endregion

        #region 其他属性
        /// <summary>
        /// 钢矩管基线
        /// </summary>
        [Browsable(false)]
        public List<DbLine> LocationCurve
        {
            get
            {
                List<DbPt> pts = GetOutPts();
                int iCt = pts.Count;        // 点的总数
                int iCt1 = iCt - 1;         // 最后一个点的索引
                List<DbLine> Linebase = new List<DbLine>();

                if (_close)
                {
                    // 处理闭合铝单板
                    for (int i = 0; i < iCt; i++)
                    {
                        int iP = i != iCt1 ? i + 1 : 0;

                        DbPt pt = pts[i];
                        DbPt ptP = pts[iP];

                        if (ptP.PtType == 0)
                        {
                            //基准线
                            var lineBase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                            Linebase.Add(lineBase);
                        }
                        else//构造函数里，保证了i + 2一定有
                        {
                            int iQ = iP != iCt1 ? i + 2 : 0;

                            //基准线
                            DbLine lineBase = new DbLine(pt.EleCopy(), pts[iQ].EleCopy(), ptP.EleCopy());
                            Linebase.Add(lineBase);
                            i++;
                        }
                    }
                }
                else // 处理非闭合多义线
                {
                    for (int i = 0; i < iCt1; i++)
                    {
                        DbPt pt = pts[i];        // 当前点
                        DbPt ptP = pts[i + 1];   // 下一个点

                        // 如果下一个点是普通点（非控制点）
                        if (ptP.PtType == 0)
                        {
                            // 创建直线段并添加到集合
                            DbLine linebase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                            Linebase.Add(linebase);
                        }
                        else //（用于创建曲线）
                        {
                            // 创建曲线段并添加到集合
                            DbLine linebase = new DbLine(pt.EleCopy(), pts[i + 2].EleCopy(), ptP.EleCopy());
                            Linebase.Add(linebase);
                            i++;
                        }
                    }
                }
                return Linebase;
            }
        }

        #endregion

        /// <summary>
        /// 无参构造函数，每个图元必须保留无参构造函数
        /// </summary>
        public SteelTubeSideType()
        {
            //每个图元必须在图元中定义是否为三维
            _if3D = false;
            LayerSet("幕墙龙骨"); // 设置图层名称
        }

        /// <summary>
        /// 钢矩管构造函数(不闭合,仅传递点集、绘制方向、其余参数外部传递）
        /// </summary>
        /// <param name="pts">钢矩管顶点</param>
        /// <param name="direction">钢矩管朝向</param>
        public SteelTubeSideType(List<DbPt> pts, bool direction)
        {
            LayerSet("幕墙龙骨"); // 设置图层名称

            _if3D = false;
            _direction = direction;
            _thickness = 3; //壁厚
            _height = 120; //高度
            _width = 60; //宽度
            _leftEdge = false; //起始端是否封口
            _rightEdge = false; //终止端是否封口

            if (pts == null || pts.Count < 2)
            {
                ConPts.Clear();
                return;
            }

            int iCt = pts.Count; // 输入顶点的总数
            int iCt1 = iCt - 1;  // 输入顶点列表的最后一个索引

            // 遍历输入的顶点列表以构建ConPts (控制点列表)
            // 循环少一次，因为我们总是处理点i和点i+1之间的线段
            for (int i = 0; i < iCt1; i++)
            {
                int iP = i + 1; // 当前处理线段的终点索引 (在输入pts列表中的索引)
                DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
                DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)

                // 判断当前线段是直线还是弧线，或者是铝单板的最后一段
                // ptP.PtType == 0 表示ptP是一个普通顶点，通常意味着 pt -> ptP 是一条直线
                // iP == iCt1 表示ptP是输入列表中的最后一个点，因此 pt -> ptP 是铝单板的最后一段
                if (ptP.PtType == 0 || iP == iCt1)
                {
                    // 处理直线段或铝单板的最后一段
                    DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                    ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                    ConPts.Add(ptT);         // 添加到ConPts列表

                    // 计算并添加当前线段的普通中点
                    DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                    ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                    ConPts.Add(ptM); // 添加到ConPts列表

                    // 如果当前处理的是输入列表中的最后一段 (即ptP是最后一个输入点)
                    if (iP == iCt1)
                    {
                        DbPt ptPT = ptP.EleCopy(); // 复制这最后一个输入点
                        ptPT.PtType = 0;           // 标记为普通顶点
                        ConPts.Add(ptPT);          // 添加到ConPts，作为铝单板的实际终点
                    }
                }
                else // 处理弧线段 (当ptP.PtType != 0 且不是最后一段时，通常表示ptP是弧中点)
                {
                    // 弧线段由三点定义：pts[i] (起点), pts[iP] (弧上一点/中点), pts[iP+1] (终点)
                    DbPt ptT = pt.EleCopy(); // 复制弧线的起点
                    ptT.PtType = 0;          // 标记为普通顶点
                    ConPts.Add(ptT);         // 添加到ConPts列表

                    int iQ = iP + 1;      // 弧线终点在输入pts列表中的索引
                    DbPt ptQ = pts[iQ];  // 获取弧线的终点

                    //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
                    DbPt ptM = GMath.GetArcMid(ptT, ptQ, ptP);

                    if (ptM != null) // 如果成功计算出精确的弧中点
                    {
                        ptM.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
                        ConPts.Add(ptM); // 添加到ConPts列表
                    }
                    else // 如果计算弧中点失败 (例如三点共线)
                    {
                        // 退而求其次，使用起点和终点的中点作为普通中点
                        ptM = 0.5 * (pt + ptQ);
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM);
                    }

                    // 如果这条弧线是输入列表中的最后一段（即ptQ是最后一个输入顶点）
                    if (iQ == iCt1)
                    {
                        DbPt ptQT = ptQ.EleCopy(); // 复制弧线的终点
                        ptQT.PtType = 0;           // 标记为普通顶点
                        ConPts.Add(ptQT);          // 添加到ConPts，作为铝单板的实际终点
                    }

                    i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
                }
            }

        }

        /// <summary>
        /// 钢矩管构造函数(可闭合,仅传递点集、绘制方向、是否闭合、其余参数外部传递）
        /// </summary>
        /// <param name="pts">钢矩管顶点</param>
        /// <param name="direction">钢矩管朝向</param>
        /// <param name="closed">闭合</param>
        public SteelTubeSideType(List<DbPt> pts, bool direction, bool closed)
        {
            LayerSet("幕墙龙骨"); // 设置图层名称

            _if3D = false;
            _direction = direction;
            _close = closed; // 设置闭合状态
            _thickness = 3; //钢矩管壁厚
            _height = 120; //钢矩管高度
            _width = 60; //宽度宽度
            _leftEdge = false; //起始端是否折边
            _rightEdge = false; //终止端是否折边


            if (pts == null || pts.Count < 2)
            {
                ConPts.Clear();
                return;
            }

            ConPts.Clear();
            int iCt = pts.Count; // 输入顶点的总数
            int iCt1 = iCt - 1;  // 输入顶点列表的最后一个索引

            // 遍历输入的顶点列表以构建ConPts (控制点列表)
            // 循环少一次，因为我们总是处理点i和点i+1之间的线段

            if (_close)
            {
                for (int i = 0; i < iCt; i++)
                {
                    int iP = i != iCt1 ? i + 1 : 0; // 当前处理线段的终点索引 (在输入pts列表中的索引)，如果是最后一个点则回到第一个点
                    DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
                    DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)
                    if (iP == 0) // 如果是闭合铝单板的最后一段
                    {
                        DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                        ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                        ConPts.Add(ptT);         // 添加到ConPts列表
                        // 计算并添加当前线段的普通中点
                        DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM); // 添加到ConPts列表

                    }
                    else if (ptP.PtType == 1)
                    {
                        // 如果当前线段是弧线段
                        DbPt ptT = pt.EleCopy(); // 复制弧线的起点
                        ptT.PtType = 0;          // 标记为普通顶点
                        ConPts.Add(ptT);         // 添加到ConPts列表

                        int iQ = iP != iCt1 ? i + 2 : 0;
                        DbPt ptQ = pts[iQ];  // 获取弧线的终点

                        //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
                        DbPt ptPT = GMath.GetArcMid(ptT, ptQ, ptP);
                        if (ptPT != null) // 如果成功计算出精确的弧中点
                        {
                            ptPT.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
                            ConPts.Add(ptPT); // 添加到ConPts列表
                        }
                        else // 如果计算弧中点失败 (例如三点共线)
                        {
                            // 退而求其次，使用起点和终点的中点作为普通中点
                            DbPt ptM = 0.5 * (pt + ptQ);
                            ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                            ConPts.Add(ptM);
                        }

                        i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
                    }
                    else // 如果当前线段是直线段
                    {
                        DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                        ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                        ConPts.Add(ptT);         // 添加到ConPts列表
                        // 计算并添加当前线段的普通中点
                        DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM); // 添加到ConPts列表
                    }
                }
            }
            else
            {
                for (int i = 0; i < iCt1; i++)
                {
                    int iP = i + 1; // 当前处理线段的终点索引 (在输入pts列表中的索引)
                    DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
                    DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)

                    // 判断当前线段是直线还是弧线，或者是铝单板的最后一段
                    // ptP.PtType == 0 表示ptP是一个普通顶点，通常意味着 pt -> ptP 是一条直线
                    // iP == iCt1 表示ptP是输入列表中的最后一个点，因此 pt -> ptP 是铝单板的最后一段
                    if (ptP.PtType == 0 || iP == iCt1)
                    {
                        // 处理直线段或铝单板的最后一段
                        DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                        ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                        ConPts.Add(ptT);         // 添加到ConPts列表

                        // 计算并添加当前线段的普通中点
                        DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM); // 添加到ConPts列表

                        // 如果当前处理的是输入列表中的最后一段 (即ptP是最后一个输入点)
                        if (iP == iCt1)
                        {
                            DbPt ptPT = ptP.EleCopy(); // 复制这最后一个输入点
                            ptPT.PtType = 0;           // 标记为普通顶点
                            ConPts.Add(ptPT);          // 添加到ConPts，作为铝单板的实际终点
                        }
                    }
                    else // 处理弧线段 (当ptP.PtType != 0 且不是最后一段时，通常表示ptP是弧中点)
                    {
                        // 弧线段由三点定义：pts[i] (起点), pts[iP] (弧上一点/中点), pts[iP+1] (终点)
                        DbPt ptT = pt.EleCopy(); // 复制弧线的起点
                        ptT.PtType = 0;          // 标记为普通顶点
                        ConPts.Add(ptT);         // 添加到ConPts列表

                        int iQ = iP + 1;      // 弧线终点在输入pts列表中的索引
                        DbPt ptQ = pts[iQ];  // 获取弧线的终点

                        //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
                        DbPt ptM = GMath.GetArcMid(ptT, ptQ, ptP);

                        if (ptM != null) // 如果成功计算出精确的弧中点
                        {
                            ptM.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
                            ConPts.Add(ptM); // 添加到ConPts列表
                        }
                        else // 如果计算弧中点失败 (例如三点共线)
                        {
                            // 退而求其次，使用起点和终点的中点作为普通中点
                            ptM = 0.5 * (pt + ptQ);
                            ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                            ConPts.Add(ptM);
                        }

                        // 如果这条弧线是输入列表中的最后一段（即ptQ是最后一个输入顶点）
                        if (iQ == iCt1)
                        {
                            DbPt ptQT = ptQ.EleCopy(); // 复制弧线的终点
                            ptQT.PtType = 0;           // 标记为普通顶点
                            ConPts.Add(ptQT);          // 添加到ConPts，作为铝单板的实际终点
                        }

                        i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
                    }
                }

            }

        }

        /// <summary>
        /// 铝钢矩管构造函数(可闭合, 线段创建钢矩管，仅传递点集、绘制方向、是否闭合）
        /// </summary>
        /// <param name="lines">铝单板线段</param>
        /// <param name="direction">铝单板朝向</param>
        /// <param name="closed">闭合</param>
        public SteelTubeSideType(List<DbLine> lines, bool direction, bool closed)
        {
            LayerSet("幕墙龙骨"); // 设置图层名称

            _if3D = false;
            _direction = direction;
            _close = closed; // 设置闭合状态
            _thickness = 3; // 壁厚
            _height = 120; //高度
            _leftEdge = false; //起始端是否封口
            _rightEdge = false; //终止端是否封口

            ConPts.Clear(); // 清空控制点列表
            var ls = lines.Select(l => l.EleCopy()).ToList();
            ls = GFunc.SortLines(lines);
            if (ls == null)
            {
                EBDB.Instance.UICmdLine("钢矩管基线不连续！", "", "");
                return;
            }

            foreach (var l in ls)
            {
                if (!l.IfArc)
                {
                    ConPts.Add(l.PtSt.EleCopy());
                }
                else
                {
                    ConPts.Add(l.PtSt.EleCopy());
                    ConPts.Add(l.PtMid.EleCopy());
                    ConPts.Last().PtType = 1;
                }
            }
            ConPts.Add(ls.Last().PtEnd.EleCopy());

        }

        ///<summary>
        ///获得钢矩管基准线的外轮廓点，去除ConPts列表中所有直线段中点，保留弯弧中点
        /// </summary>
        ///<param name="ifRetureArcMid">是否返回弧线中点</param>
        ///<returns></returns>
        public List<DbPt> GetOutPts(bool ifRetureArcMid = true)
        {
            List<DbPt> pts = new List<DbPt>();
            if (ifRetureArcMid)
            {
                foreach (DbPt pt in ConPts) { if (pt.PtType != 2) { pts.Add(pt); } }
            }
            else
            {
                foreach (DbPt pt in ConPts) { if (pt.PtType == 0) { pts.Add(pt); } }
            }
            return pts;
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter">写入流</param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //图元类标识，必要
            binaryWriter.Write(this.GetType().ToString());

            //版本号，必要，每次版本的升级必须注明时间和变更内容
            //0版：2024.10.30 新增
            binaryWriter.Write(0);

            //图元共有参数，必要，该方法对DbElement类的公共数据进行了保存
            PubSave(binaryWriter);

            //分界线之上的代码为每个图元的固定格式，不能修改
            //-------分界线---------
            //分界线之下的代码为每个图元特有参数，根据图元的需要确定参数是否保存
            //在进行数据保存时，应确保保存的参数尽可能精简

            //图元特有参数,根据每个图元情况进行保存
            binaryWriter.Write(_thickness);
            binaryWriter.Write(_height);
            binaryWriter.Write(_width);
            binaryWriter.Write(_direction);
            binaryWriter.Write(_close);
            binaryWriter.Write(_length);
            binaryWriter.Write(_autoScale);
            binaryWriter.Write(_leftEdge);
            binaryWriter.Write(_rightEdge);
            binaryWriter.Write((Int16)_surfaceTreatment);
            _material.DataSave(binaryWriter);
        }

        /// <summary>
        /// 装载数据
        /// </summary>
        /// <param name="binaryReader">读取流</param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            //图元数据的读顺序必须与图元的写顺序完全一致
            //图元的类标识平台已经读取了，在图元中不需要再读
            //在图元的读写方法中不能使用try/catch对错误进行捕捉，
            //读写中不能容忍出现未知的异常

            //每个图元的版本号是相互独立的，并且与软件整体的版本号无关
            //版本号的设定是为了解决软件发布后，在类数据变化时，高版本能兼容低版本的问题
            //在写图元时，必须完全理解版本号的运作逻辑
            //对版本号的理解如果存在偏差，可能在软件发布后导致用户数据丢失等严重后果！

            //读取版本号，必要
            int VerNum = binaryReader.ReadInt32();

            //每个版本号的读写都必须保留
            if (VerNum == 0)
            {
                #region
                //图元共有参数，必要，该方法对DbElement类的公共数据进行了读取
                PubLoad(binaryReader);

                //图元特有参数
                _thickness = binaryReader.ReadDouble();
                _height = binaryReader.ReadDouble();
                _width = binaryReader.ReadDouble();
                _direction = binaryReader.ReadBoolean();
                _close = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _autoScale = binaryReader.ReadBoolean();
                _leftEdge = binaryReader.ReadBoolean();
                _rightEdge = binaryReader.ReadBoolean();
                _surfaceTreatment = (SurfaceTreatmentType)binaryReader.ReadInt16();
                _material.DataLoad(binaryReader);
                #endregion
            }
        }

        /// <summary>
        /// 返回图元的一个备份(深度复制)，每个图元必须重写该方法
        /// </summary>
        /// <param name="changeUid">深度复制时是否改变 UniqueId true：改变 false：不改变</param>
        /// <returns>新的图元</returns>
        public override DbElement EleCopy(bool changeUid = false)
        {
            //在C#中，两个类如果直接赋值，传递的是引用，两个类本身均指向同一地址，
            //修改任意一个，两个都会被修改
            //图元深度复制的原理是将图元的每个参数深入到值类型进行赋值，
            //确保深度复制后的图元与原图元可以独立修改而不互相影响

            SteelTubeSideType ele = new SteelTubeSideType();

            //图元共有参数，必要
            PubCopy(ele);

            //图元特有参数
            ele._thickness = _thickness;
            ele._height = _height;
            ele._width = _width;
            ele._direction = _direction;
            ele._close = _close;
            ele._length = _length;
            ele._autoScale = _autoScale;
            ele._leftEdge = _leftEdge;
            ele._rightEdge = _rightEdge;
            ele._surfaceTreatment = _surfaceTreatment;
            ele._material = _material;

            if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); } //改变Uid
            return ele;
        }

        /// <summary>
        /// 激活，图元激活的本质是通过"控制点"+"参数"计算出所有构成图元的点、线、文字、填充等基本要素
        /// </summary>
        public override void Activate()
        {
            //在图元被移动、拉伸等变化时，图元的动画是通过不断的调用激活函数形成的，
            //因此在每次激活时，需要清空之前计算存储的数据
            //图元的显示要素存储的列表包括：Lines、Texts、Circles、Hatchs四个结合，可以根据图元需要进行选用

            // 设置缩放比例，如果启用了自动缩放，则使用视图比例
            double tempSc = 1;
            if (_autoScale) { tempSc = ViewSc / 100.0; }

            Lines.Clear();

            // 获取钢矩管的所有点（不包括直线段中点）
            List<DbPt> pts = GetOutPts();

            int iCt = pts.Count;        // 点的总数
            int iCt1 = iCt - 1;         // 最后一个点的索引
            double wallThicknessHeight = _height - _thickness; // 内壁线高度

            // 初始化钢矩管长度
            _length = 0;

            if (_close)
            {
                // 计算偏移点集
                List<DbPt> innerLinePts1 = GMath.GetOffsetArcPts(pts, _thickness, _direction); //内壁线1点集
                List<DbPt> innerLinePts2 = GMath.GetOffsetArcPts(pts, wallThicknessHeight, _direction); //内壁线2点集
                List<DbPt> outLinePts = GMath.GetOffsetArcPts(pts, _height, _direction); // 外壁线点集

                // 检查偏移是否成功（偏移失败时返回null）
                if (innerLinePts1 == null) return;
                if (innerLinePts2 == null) return;
                if (outLinePts == null) return;

                List<DbLine> innerLines1 = new List<DbLine>();
                List<DbLine> innerLines2 = new List<DbLine>();

                // 处理闭合铝单板
                for (int i = 0; i < iCt; i++)
                {
                    int iP = i != iCt1 ? i + 1 : 0;

                    DbPt pt = pts[i];
                    DbPt ptP = pts[iP];
                    DbPt innerLinePt1 = innerLinePts1[i];
                    DbPt innerLinePtP1 = innerLinePts1[iP];
                    DbPt innerLinePt2 = innerLinePts2[i];
                    DbPt innerLinePtP2 = innerLinePts2[iP];
                    DbPt outLinePt = outLinePts[i];
                    DbPt outLinePtP = outLinePts[iP];

                    if (ptP.PtType == 0)
                    {
                        //基准线
                        DbLine lineBase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        //内壁线1
                        DbLine innerLine1 = new DbLine(innerLinePt1.EleCopy(), innerLinePtP1.EleCopy());
                        innerLines1.Add(innerLine1);

                        //内壁线2
                        DbLine innerLine2 = new DbLine(innerLinePt2.EleCopy(), innerLinePtP2.EleCopy());
                        innerLines2.Add(innerLine2);

                        //外壁线
                        DbLine outLine = new DbLine(outLinePt.EleCopy(), outLinePtP.EleCopy());
                        Lines.Add(outLine);
                    }
                    else//构造函数里，保证了i + 2一定有
                    {
                        int iQ = iP != iCt1 ? i + 2 : 0;

                        //基准线
                        DbLine lineBase = new DbLine(pt.EleCopy(), pts[iQ].EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        //内壁线1
                        DbLine innerLine1 = new DbLine(innerLinePt1.EleCopy(), innerLinePts1[iQ].EleCopy(), innerLinePtP1.EleCopy());
                        innerLines1.Add(innerLine1);

                        //内壁线2
                        DbLine innerLine2 = new DbLine(innerLinePt2.EleCopy(), innerLinePts2[iQ].EleCopy(), innerLinePtP2.EleCopy());
                        innerLines2.Add(innerLine2);

                        //外壁线
                        DbLine outLine = new DbLine(outLinePt.EleCopy(), outLinePts[iQ].EleCopy(), outLinePtP.EleCopy());
                        Lines.Add(outLine);

                        i++;
                    }
                }
                // 设置所有线段的状态、图层、样式、颜色和宽度
                // 设置外壁线及转折线
                foreach (DbLine line in Lines) { line.SetStatus(1, 1, 1); }
                foreach (DbLine line in Lines) { line.LayerId = PreLayerManage.GetLayerId("金属面材"); }
                LayerChange(layerManage.GetLayer(_layerId));

                // 设置内壁线
                foreach (DbLine line in innerLines1)
                {
                    //line.SetStatus(1, 1, 1); // 设置状态
                    line.ColorIndex = 252; // 灰色
                    line.StyleIndex = 1; // 虚线
                }
                foreach (DbLine line in innerLines2)
                {
                    //line.SetStatus(1, 1, 1); // 设置状态
                    line.ColorIndex = 252; // 灰色
                    line.StyleIndex = 1; // 虚线
                }
                Lines.AddRange(innerLines1); // 添加内壁线1到Lines集合
                Lines.AddRange(innerLines2);
            }
            else // 处理非闭合钢矩管
            {
                // 计算偏移点集
                List<DbPt> innerLinePts1 = GMath.GetUnclosedOffsetArcPts(pts, _thickness, _direction); //内壁线1点集
                List<DbPt> innerLinePts2 = GMath.GetUnclosedOffsetArcPts(pts, wallThicknessHeight, _direction); //内壁线2点集
                List<DbPt> outLinePts = GMath.GetUnclosedOffsetArcPts(pts, _height, _direction); // 外壁线点集

                // 检查偏移是否成功（偏移失败时返回null）
                if (innerLinePts1 == null) return;
                if (innerLinePts2 == null) return;
                if (outLinePts == null) return;

                // === 计算端头封口的交点（统一处理）
                DbPt leftInnerPt1 = null;
                DbPt leftInnerPt2 = null;
                DbPt leftOutterPt = null;
                DbPt leftBasePt = null;
                DbPt rightInnerPt1 = null;
                DbPt rightInnerPt2 = null;
                DbPt rightOutterPt = null;
                DbPt rightBasePt = null;

                // 计算左端封口线交点
                if (_leftEdge)
                {
                    DbLine leftSeamLine = GMath.LineOffset(
                        new DbLine(pts[0].EleCopy(), outLinePts[0].EleCopy()),
                        !_direction, _thickness);

                    // 判断第一段是直线还是弧线
                    if (pts[1].PtType == 0) // 第一段是直线
                    {
                        leftInnerPt1 = GMath.GetTwoLineCrossPt(leftSeamLine,
                            new DbLine(innerLinePts1[0].EleCopy(), innerLinePts1[1].EleCopy()));
                        leftInnerPt2 = GMath.GetTwoLineCrossPt(leftSeamLine,
                            new DbLine(innerLinePts2[0].EleCopy(), innerLinePts2[1].EleCopy()));
                        leftBasePt = GMath.GetTwoLineCrossPt(leftSeamLine,
                            new DbLine(pts[0].EleCopy(), pts[1].EleCopy()));
                        leftOutterPt = GMath.GetTwoLineCrossPt(leftSeamLine,
                            new DbLine(outLinePts[0].EleCopy(), outLinePts[1].EleCopy()));


                    }
                    else // 第一段是弧线
                    {
                        // 创建内壁线和外壁线的弧线
                        DbLine innerArc1 = new DbLine(innerLinePts1[0].EleCopy(), innerLinePts1[2].EleCopy(), innerLinePts1[1].EleCopy());
                        DbLine innerArc2 = new DbLine(innerLinePts2[0].EleCopy(), innerLinePts2[2].EleCopy(), innerLinePts2[1].EleCopy());
                        DbLine outterArc = new DbLine(outLinePts[0].EleCopy(), outLinePts[2].EleCopy(), outLinePts[1].EleCopy());
                        DbLine baseArc = new DbLine(pts[0].EleCopy(), pts[2].EleCopy(), pts[1].EleCopy());

                        leftBasePt = GMath.GetTwoLineCrossPt(leftSeamLine, baseArc);
                        leftInnerPt1 = GMath.GetTwoLineCrossPt(leftSeamLine, innerArc1);
                        leftInnerPt2 = GMath.GetTwoLineCrossPt(leftSeamLine, innerArc2);
                        leftOutterPt = GMath.GetTwoLineCrossPt(leftSeamLine, outterArc);
                    }
                }

                // 计算右端封口线交点
                if (_rightEdge)
                {
                    DbLine rightSeamLine = GMath.LineOffset(
                        new DbLine(pts[iCt1].EleCopy(), outLinePts[iCt1].EleCopy()),
                        _direction, _thickness);

                    // 判断最后一段是直线还是弧线
                    if (pts[iCt1 - 1].PtType != 1) // 最后一段是直线（倒数第二个点不是弧中点）
                    {
                        rightInnerPt1 = GMath.GetTwoLineCrossPt(rightSeamLine,
                            new DbLine(innerLinePts1[iCt1 - 1].EleCopy(), innerLinePts1[iCt1].EleCopy()));
                        rightInnerPt2 = GMath.GetTwoLineCrossPt(rightSeamLine,
                            new DbLine(innerLinePts2[iCt1 - 1].EleCopy(), innerLinePts2[iCt1].EleCopy()));
                        rightBasePt = GMath.GetTwoLineCrossPt(rightSeamLine,
                            new DbLine(pts[iCt1 - 1].EleCopy(), pts[iCt1].EleCopy()));
                        rightOutterPt = GMath.GetTwoLineCrossPt(rightSeamLine,
                            new DbLine(outLinePts[iCt1 - 1].EleCopy(), outLinePts[iCt1].EleCopy()));
                    }
                    else // 最后一段是弧线
                    {
                        // 创建厚度线弧和折边线弧
                        DbLine innerArc1 = new DbLine(innerLinePts1[iCt1 - 2].EleCopy(), innerLinePts1[iCt1].EleCopy(), innerLinePts1[iCt1 - 1].EleCopy());
                        DbLine innerArc2 = new DbLine(innerLinePts2[iCt1 - 2].EleCopy(), innerLinePts2[iCt1].EleCopy(), innerLinePts2[iCt1 - 1].EleCopy());
                        DbLine outterArc = new DbLine(outLinePts[iCt1 - 2].EleCopy(), outLinePts[iCt1].EleCopy(), outLinePts[iCt1 - 1].EleCopy());
                        DbLine baseArc = new DbLine(pts[iCt1 - 2].EleCopy(), pts[iCt1].EleCopy(), pts[iCt1 - 1].EleCopy());

                        rightBasePt = GMath.GetTwoLineCrossPt(rightSeamLine, baseArc);
                        rightInnerPt1 = GMath.GetTwoLineCrossPt(rightSeamLine, innerArc1);
                        rightInnerPt2 = GMath.GetTwoLineCrossPt(rightSeamLine, innerArc2);
                        rightOutterPt = GMath.GetTwoLineCrossPt(rightSeamLine, outterArc);
                    }
                }

                // === 主循环：处理每个线段
                for (int i = 0; i < iCt1; i++)
                {
                    DbPt pt = pts[i];        // 当前点
                    DbPt ptP = pts[i + 1];   // 下一个点
                    DbPt innerLinePt1 = innerLinePts1[i]; // 内壁线1点
                    DbPt innerLinePtP1 = innerLinePts1[i + 1]; // 内壁线1下一个点
                    DbPt innerLinePt2 = innerLinePts2[i]; // 内壁线2点
                    DbPt innerLinePtP2 = innerLinePts2[i + 1]; // 内壁线2下一个点
                    DbPt outLinePt = outLinePts[i]; // 外壁线点
                    DbPt outLinePtP = outLinePts[i + 1]; // 外壁线下一个点

                    // 绘制基准线
                    if (ptP.PtType == 0) // 直线段
                    {
                        DbLine lineBase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        // 外壁线
                        DbLine outterLine = new DbLine(outLinePt.EleCopy(), outLinePtP.EleCopy());
                        Lines.Add(outterLine);

                    }
                    else // 弧线段
                    {
                        DbLine lineBase = new DbLine(pt.EleCopy(), pts[i + 2].EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        DbLine outterLine = new DbLine(outLinePts[i].EleCopy(), outLinePts[i + 2].EleCopy(), outLinePts[i + 1].EleCopy());
                        Lines.Add(outterLine);

                        i++; // 跳过弧线中点
                    }
                }

                // === 绘制内壁线（统一处理）
                // 内壁线1
                List<DbLine> innerLines1 = DrawInnerLines(innerLinePts1, leftInnerPt1, rightInnerPt1, pts, _leftEdge, _rightEdge);
                // 内壁线2
                List<DbLine> innerLines2 = DrawInnerLines(innerLinePts2, leftInnerPt2, rightInnerPt2, pts, _leftEdge, _rightEdge);

                // === 绘制端部线条
                for (int i = 0; i < iCt; i++)
                {
                    if (pts[i].PtType != 1)
                    {
                        Lines.Add(new DbLine(pts[i].EleCopy(), innerLinePts1[i].EleCopy()));
                        // 绘制从内壁线1到内壁线2的连接线
                        Lines.Add(new DbLine(innerLinePts1[i].EleCopy(), innerLinePts2[i].EleCopy()));
                        // 绘制从内壁线2到外壁线的连接线
                        Lines.Add(new DbLine(innerLinePts2[i].EleCopy(), outLinePts[i].EleCopy()));
                    }
                }

                // === 绘制封口线条
                // 左端处理
                if (_leftEdge && leftOutterPt != null && leftBasePt != null)
                {
                    // 绘制端部折边线
                    Lines.Add(new DbLine(leftBasePt.EleCopy(), leftOutterPt.EleCopy()));
                }

                // 右端处理
                if (_rightEdge && rightBasePt != null && rightOutterPt != null)
                {
                    Lines.Add(new DbLine(rightBasePt.EleCopy(), rightOutterPt.EleCopy()));
                }


                foreach (DbLine line in innerLines1)
                {
                    line.ColorIndex = 252; // 灰色
                    line.StyleIndex = 1; //虚线
                }

                // 设置所有线段的状态、图层、样式、颜色和宽度
                // 设置外壁线及转折线
                foreach (DbLine line in Lines) { line.SetStatus(1, 1, 1); }
                foreach (DbLine line in Lines) { line.LayerId = PreLayerManage.GetLayerId("金属面材"); }
                LayerChange(layerManage.GetLayer(_layerId));

                foreach (DbLine line in innerLines1)
                {
                    line.SetStatus(1, 1, 1); // 设置状态
                    line.ColorIndex = 252; // 灰色
                    line.StyleIndex = 1; // 虚线
                }

                foreach (DbLine line in innerLines2)
                {
                    line.SetStatus(1, 1, 1); // 设置状态
                    line.ColorIndex = 252; // 灰色
                    line.StyleIndex = 1; // 虚线
                }

                Lines.AddRange(innerLines1); // 添加内壁线1
                Lines.AddRange(innerLines2); // 添加内壁线2
            }

            //if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
            //if (_colorIndex >= 0)
            //{
            //    foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; }
            //}

            //if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

            EleArea = new DbEleArea(this);
            CalSolid2D();
        }

        /// <summary>
        /// 绘制内壁线的辅助方法
        /// </summary>
        /// <param name="innerLinePts">内壁线偏移点集</param>
        /// <param name="leftInnerPt">左封口线与内壁线的交点</param>
        /// <param name="rightInnerPt">右封口线与内壁线的交点</param>
        /// <param name="pts">原始点集</param>
        public static List<DbLine> DrawInnerLines(List<DbPt> innerLinePts, DbPt leftInnerPt, DbPt rightInnerPt, List<DbPt> pts, bool leftEdge, bool rightEdge)
        {
            int iCt = pts.Count;
            int iCt1 = iCt - 1;
            List<DbLine> innerLines = new List<DbLine>();

            // 如果偏移点集为空，返回空列表
            if (innerLinePts == null || innerLinePts.Count == 0) return innerLines;

            // 如果只有两个点（一条直线）
            if (iCt == 2)
            {
                DbPt startPt = leftEdge && leftInnerPt != null ? leftInnerPt : innerLinePts[0];
                DbPt endPt = rightEdge && rightInnerPt != null ? rightInnerPt : innerLinePts[iCt1];
                innerLines.Add(new DbLine(startPt.EleCopy(), endPt.EleCopy()));
                return innerLines;
            }

            // 处理多段线的内壁线
            for (int i = 0; i < iCt1; i++)
            {
                DbPt pt = pts[i];        // 当前基准点
                DbPt ptNext = pts[i + 1]; // 下一个基准点

                // 确定当前段的起点
                DbPt currentStart;
                if (i == 0 && leftEdge && leftInnerPt != null)
                {
                    currentStart = leftInnerPt.EleCopy();
                }
                else
                {
                    currentStart = innerLinePts[i].EleCopy();
                }

                // 判断当前段是直线还是弧线
                if (ptNext.PtType == 0) // 直线段
                {
                    // 确定当前段的终点
                    DbPt currentEnd;
                    if (i == iCt1 - 1 && rightEdge && rightInnerPt != null)
                    {
                        currentEnd = rightInnerPt.EleCopy();
                    }
                    else
                    {
                        currentEnd = innerLinePts[i + 1].EleCopy();
                    }

                    // 创建内壁线直线段
                    innerLines.Add(new DbLine(currentStart, currentEnd));
                }
                else // 弧线段
                {
                    // 检查是否有足够的点来构成弧线
                    if (i + 2 < iCt)
                    {
                        // 确定弧线的终点
                        DbPt currentEnd;
                        if (i + 2 == iCt1 && rightEdge && rightInnerPt != null)
                        {
                            currentEnd = rightInnerPt.EleCopy();
                        }
                        else
                        {
                            currentEnd = innerLinePts[i + 2].EleCopy();
                        }

                        // 获取弧线中点
                        DbPt arcMidPt = innerLinePts[i + 1].EleCopy();

                        // 重新计算弧线中点以确保弧线的正确性
                        DbPt correctMidPt = GMath.GetArcMid(currentStart, currentEnd, arcMidPt);

                        if (correctMidPt != null)
                        {
                            // 创建内壁线弧线段
                            DbLine innerArc = new DbLine(currentStart, currentEnd, correctMidPt);
                            innerLines.Add(innerArc);
                        }
                        else
                        {
                            // 如果无法计算正确的弧线中点，退化为直线
                            innerLines.Add(new DbLine(currentStart, currentEnd));
                        }

                        i++; // 跳过弧线中点
                    }
                    else
                    {
                        // 如果没有足够的点构成弧线，创建直线到最后一个点
                        DbPt currentEnd;
                        if (rightEdge && rightInnerPt != null)
                        {
                            currentEnd = rightInnerPt.EleCopy();
                        }
                        else
                        {
                            currentEnd = innerLinePts[iCt1].EleCopy();
                        }
                        innerLines.Add(new DbLine(currentStart, currentEnd));
                    }
                }
            }

            return innerLines;
        }

        /// <summary>
        /// 返回图元被单独选中时显示的提示内容
        /// </summary>
        /// <param name="str1">提示文字1</param>
        /// <param name="str2">提示文字2</param>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = _height.ToString() + "X" + _width.ToString() + "X" + _thickness.ToString() + "钢矩管"; 
            str2 = _surfaceTreatment.GetDescription();
        }

        ///<summary>
        ///检查圆弧中点是否正确
        /// </summary>
        /// <returns></returns>
        private bool IfHaveWrongArcMid(List<DbPt> pts)
        {
            int iCt = pts.Count; //控制点总数
            int iCt1 = iCt - 1; //控制点列表的最后一个索引
            int iCt2 = iCt - 2; //控制点列表的倒数第二个索引

            if (_close)
            {
                //遍历所有顶点（每次递增2）
                for (int i = 0; i < iCt1; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    DbPt ptM = pts[iM]; //获取中点
                    if (ptM.PtType != 1) { continue; }
                    int ip = i != iCt2 ? i + 2 : 0; //终点（下一个点索引）
                    DbPt pt = pts[i]; //起点
                    DbPt ptP = pts[ip]; //终点
                    DbPt ptC = GMath.GetArcCenter(pt, ptP, ptM); //计算圆心
                    if (ptC == null) { return true; } //如果无法计算圆心，则返回true表示有错误
                }
            }
            else
            {
                //遍历所有顶点（每次递增2）
                for (int i = 0; i < iCt2; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    DbPt ptM = pts[iM]; //获取中点
                    if (ptM.PtType != 1) { continue; }
                    int ip = i + 2; //终点（下一个点索引）
                    DbPt pt = pts[i]; //起点
                    DbPt ptP = pts[ip]; //终点
                    DbPt ptC = GMath.GetArcCenter(pt, ptP, ptM); //计算圆心
                    if (ptC == null) { return true; } //如果无法计算圆心，则返回true表示有错误
                }
            }
            return false;
        }

        ///<summary>
        ///刷新图元在拉伸后的状态
        /// </summary>
        ///<param name="X">X方向的拉伸距离</param> 
        ///<param name="Y">X方向的拉伸距离</param> 
        public override void EleMove_s(double X, double Y)
        {
            //复制一份控制点
            List<DbPt> pts = GFunc.CopyPts(ConPts);

            double X2 = X * 0.5; //X方向拉伸的半径
            double Y2 = Y * 0.5; //Y方向拉伸的半径

            //标记是否有点被处理过、是否存在无法处理的情况
            bool ifHave = false;  //是否被拉伸处理过
            int iCt = pts.Count;  //控制点总数
            int iCt1 = iCt - 1;   //控制点列表的最后一个索引  
            int iCt2 = iCt - 2;   //控制点列表的倒数第二个索引
            bool ifError = false; //是否存在无法处理的情况(如弧中点无法重新计算）

            //处理闭合的钢矩管
            if (_close)
            {
                //遍历所有顶点（跳过中点，每次递增2）
                for (int i = 0; i < iCt1; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    int iP = i != iCt2 ? i + 2 : 0; //终点（下一个点索引）
                    DbPt pt = pts[i];
                    DbPt ptP = pts[iP];
                    DbPt ptM = pts[iM]; //中点

                    //情况1：起点和终点都被选中，整段一起移动
                    if (pt.Status == 1 && ptP.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动
                        ptP.MoveSelf(X, Y); //终点移动
                        ifHave = true; //标记为被处理过
                    }
                    //情况2：只有起点被选中
                    else if (pt.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动

                        //如果是圆弧中点，需要重新计算中点位置以保持圆弧的形状
                        if (ptM.PtType == 1)
                        {
                            //根据新起点和原中点重新计算圆弧中点位置
                            DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            //更新中点坐标
                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z; //更新Z坐标
                        }
                        else //普通中点
                        {
                            ptM.MoveSelf(X2, Y2);
                        }
                        ifHave = true; //标记为被处理过
                    }
                    //情况3：只有终点被选中
                    else if (ptP.Status == 1)
                    {
                        //圆弧中点需要特殊处理
                        if (ptM.PtType == 1)
                        {
                            //获取移动后的终点
                            DbPt ptPT;
                            if (iP == 0) { ptPT = ptP.EleCopy(); } //如果首尾相连，则终点为首点复制一点
                            else { ptPT = ptP.Move(X, Y); }

                            //根据原起点和新终点重新计算圆弧中点
                            DbPt ptTemp = GMath.GetArcMid(pt, ptPT, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            //更新中点坐标
                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z;
                        }
                        else //普通中点
                        {
                            ptM.MoveSelf(X2, Y2);
                        }
                        ifHave = true; //标记为被处理过
                    }
                }

            }
            //处理非闭合钢矩管
            else
            {
                for (int i = 0; i < iCt2; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    int iP = i + 2; //终点（下一个点索引）
                    DbPt pt = pts[i];
                    DbPt ptP = pts[iP];
                    DbPt ptM = pts[iM]; //中点

                    //情况1：起点和终点都被选中，整段一起移动
                    if (pt.Status == 1 && ptP.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动
                        ptP.MoveSelf(X, Y); //终点移动
                        ifHave = true; //标记为被处理过
                    }
                    //情况2：只有起点被选中
                    else if (pt.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动

                        //如果是圆弧中点，需要重新计算中点位置以保持圆弧的形状
                        if (ptM.PtType == 1)
                        {
                            DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z;
                        }
                        else //普通中点
                        {
                            ptM.MoveSelf(X2, Y2);
                        }
                        ifHave = true; //标记为被处理过
                    }
                    //情况3：只有终点被选中
                    else if (ptP.Status == 1)
                    {
                        if (ptM.PtType == 1)
                        {
                            DbPt ptPT = ptP.Move(X, Y); //如果是圆弧中点，复制终点
                            DbPt ptTemp = GMath.GetArcMid(pt, ptPT, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z;
                        }
                        else
                        {
                            ptM.MoveSelf(X2, Y2); //普通中点
                        }
                        //如果是最后一个点，也需要移动
                        if (iP == iCt1) { ptP.MoveSelf(X, Y); } //终点移动
                        ifHave = true;
                    }
                }
            }

            //无错误时，处理选中中点的情况
            if (!ifError && !ifHave)
            {
                //处理闭合
                if (_close)
                {
                    //遍历所有顶点，查找被选中的中点
                    for (int i = 0; i < iCt1; i = i + 2)
                    {
                        int iM = i + 1;
                        int iP = i != iCt2 ? i + 2 : 0; //终点（下一个点索引）
                        DbPt pt = pts[i];
                        DbPt ptP = pts[iP];
                        DbPt ptM = pts[iM]; //中点

                        //如果中点被选中
                        if (ptM.Status == 1)
                        {
                            //情况1：普通中点被选中
                            if (ptM.PtType == 2)
                            {
                                //移动整个线段的三个点
                                pt.MoveSelf(X, Y); //起点移动
                                ptP.MoveSelf(X, Y); //终点移动
                                ptM.MoveSelf(X, Y);

                                //处理前一个中点
                                int iL = i != 0 ? i - 1 : iCt1; //前一个点索引
                                DbPt ptL = pts[iL]; //前一个点
                                ptL.MoveSelf(X2, Y2); //前一个中点移动

                                //如果前一个中点是圆弧中点，需要重新计算位置
                                if (ptL.PtType == 1)
                                {
                                    //根据前一个起点和新的当前起点重新计算圆弧中点位置
                                    DbPt ptTemp = GMath.GetArcMid(pts[iL - 1], pt, ptM);
                                    if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误

                                    //更新前一个中点坐标
                                    ptL.X = ptTemp.X;
                                    ptL.Y = ptTemp.Y;
                                    ptL.Z = ptTemp.Z;
                                }

                                //处理后一个中点
                                DbPt ptR = pts[iP + 1]; //后一个点
                                ptR.MoveSelf(X2, Y2); //后一个中点移动

                                //如果后一个中点是圆弧中点，需要重新计算位置
                                if (ptR.PtType == 1)
                                {
                                    int iQ = iP != iCt2 ? iP + 2 : 0; //后一个点的终点索引

                                    //根据新的当前终点和后一个终点重新计算圆弧中点位置
                                    DbPt ptTemp = GMath.GetArcMid(ptP, pts[iQ], ptR);
                                    if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误

                                    //更新后一个中点坐标
                                    ptR.X = ptTemp.X;
                                    ptR.Y = ptTemp.Y;
                                    ptR.Z = ptTemp.Z;
                                }
                            }
                            //情况2：弧线中点被选中
                            else
                            {
                                ptM.MoveSelf(X, Y); //移动圆弧中点

                                //根据原始起点和终点重新计算圆弧中点位置
                                DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                                if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误

                                ptM.X = ptTemp.X;
                                ptM.Y = ptTemp.Y;
                                ptM.Z = ptTemp.Z;
                            }//圆弧中点只移动自己
                            break;
                        }
                    }
                }
                //处理非闭合
                else
                {
                    for (int i = 0; i < iCt2; i = i + 2)
                    {
                        int iM = i + 1; //中点索引
                        int iP = i + 2; //终点（下一个点索引）
                        DbPt pt = pts[i];
                        DbPt ptP = pts[iP];
                        DbPt ptM = pts[iM];

                        //如果中点被选中
                        if (ptM.Status == 1)
                        {
                            //情况1：普通中点
                            if (ptM.PtType == 2)
                            {
                                //移动整个线段的三个点
                                pt.MoveSelf(X, Y); //起点移动
                                ptP.MoveSelf(X, Y); //终点移动
                                ptM.MoveSelf(X, Y); //中点移动

                                //处理前一个中点(如果存在)
                                if (i >= 2)
                                {
                                    DbPt ptT = pts[i - 1]; //前一个中点
                                    ptT.MoveSelf(X2, Y2); //前一个中点移动

                                    //如果前一个中点是圆弧中点，需要重新计算位置
                                    if (ptT.PtType == 1)
                                    {
                                        //根据前一个起点和新的当前起点重新计算圆弧中点位置
                                        DbPt ptTemp = GMath.GetArcMid(pts[i - 2], pt, ptM);
                                        if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误
                                        //更新前一个中点坐标
                                        ptT.X = ptTemp.X;
                                        ptT.Y = ptTemp.Y;
                                        ptT.Z = ptTemp.Z;
                                    }
                                }

                                //处理后一个中点（如果存在）
                                if (i + 3 < iCt1)
                                {
                                    DbPt ptT = pts[i + 3];
                                    ptT.MoveSelf(X2, Y2); //后一个中点移动

                                    //如果后一个中点是圆弧中点，需要重新计算位置
                                    if (ptT.PtType == 1)
                                    {
                                        //根据新的当前终点和后一个终点重新计算圆弧中点位置
                                        DbPt ptTemp = GMath.GetArcMid(ptP, pts[i + 4], ptT);
                                        if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误
                                        //更新后一个中点坐标
                                        ptT.X = ptTemp.X;
                                        ptT.Y = ptTemp.Y;
                                        ptT.Z = ptTemp.Z;
                                    }
                                }

                            }
                            //情况2： 弧线中点
                            else
                            {
                                ptM.MoveSelf(X, Y); //移动圆弧中点
                                //根据原始起点和终点重新计算圆弧中点位置
                                DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                                if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误
                                ptM.X = ptTemp.X;
                                ptM.Y = ptTemp.Y;
                                ptM.Z = ptTemp.Z;
                            }//圆弧中点只移动自己
                            break; //找到被选中的中点后，退出循环
                        }
                    }
                }
            }

            //如果没有错误且所有圆弧中点都能正确计算，则应用拉伸结果
            if (!ifError && !IfHaveWrongArcMid(pts))
            {
                ConPts = pts; //更新控制点列表
                Activate(); //重新激活图元以更新显示
            }
        }

        ///<summary>
        ///刷新钢矩管移动后的数据
        ///</summary>
        ///<param name="X">X方向的移动距离</param>
        ///<param name="Y">Y方向的移动距离</param>
        public override void EleMove(double X, double Y)
        {
            //铝钢矩管的移动是通过控制点的移动来实现的
            //如果钢矩管被选中，则移动所有控制点
            if (ConPts.Count > 0)
            {
                foreach (DbPt pt in ConPts)
                {
                    pt.MoveSelf(X, Y); //移动控制点
                }
                ActAndCal2D(); //激活并计算2D实体
            }
        }

        /// <summary>
        /// 图元图层或线型属性被用户修改时调用
        /// </summary>
        // 颜色变更处理：根据颜色索引设置钢矩管所有线条的颜色
        public override void StyleChange()
        {
            CalSolid2D();
        }

        #region 常规几何修改
        /// <summary>
        /// 基于线的图元延伸到其他图元的线、圆时的行为：点选模式
        /// 将钢矩管的基线延伸到与其他图元相交，支持延伸到线条和圆形
        /// </summary>
        /// <param name="eles">目标图元集合，钢矩管将延伸到这些图元</param>
        /// <param name="pt">点击点，用于确定延伸方向和目标</param>
        /// <returns>延伸成功返回true，失败返回false</returns>
        //public override bool EleExtendWithElesByPt(List<DbElement> eles, DbPt pt)
        //{
        //    if (_close) //闭合钢矩管不支持延伸
        //    {
        //        EBDB.Instance.UICmdLine("闭合钢矩管不支持延伸!", null, null);
        //        return false;
        //    }
        //    // 获取钢矩管的基线用于延伸
        //    List<DbLine> lineToExtend = this.LocationCurve;
        //    if (lineToExtend == null || lineToExtend.Count == 0) return false;

        //    // 根据点击点pt确定要延伸的线段(最近的线段)，返回该线段及其在整体中的索引
        //    var nearestLine = GFunc.GetNearLine(lineToExtend, pt, out int index);
        //    if (nearestLine == null || nearestLine.GetLong() < 1.0) return false;

        //    if (!nearestLine.IfArc)
        //    {
        //        #region 直线段处理逻辑
        //        GuiDB.Vector vec;  // 延伸方向向量
        //        DbPt ptStart;  // 起点

        //        //确定延伸方向和起始点
        //        if (lineToExtend.Count == 1)//单线段
        //        {
        //            // 根据点击点距离线段两端的远近来判断延伸方向
        //            // 距离较远的端点作为延伸起始点（即从较近的端点向较远的端点延伸）
        //            ptStart = GMath.Distance(pt, nearestLine.PtEnd) > GMath.Distance(pt, nearestLine.PtSt) ? nearestLine.PtSt : nearestLine.PtEnd;
        //            //创建从线段中点指向延伸起始点的方向向量
        //            vec = new GuiDB.Vector(nearestLine.PtMid, ptStart);
        //        }
        //        else //多线段
        //        {
        //            // 根据线段在整体中的位置（索引）确定延伸方向
        //            if (index == 0)
        //            {
        //                vec = new GuiDB.Vector(nearestLine.PtMid, nearestLine.PtSt);
        //                ptStart = nearestLine.PtSt;
        //            }
        //            else //最后一段线段，从终点延伸
        //            {
        //                vec = new GuiDB.Vector(nearestLine.PtMid, nearestLine.PtEnd);
        //                ptStart = nearestLine.PtEnd;
        //            }
        //        }

        //        //寻找延伸目标点
        //        DbPt result = null; //存储计算出的延伸结果点

        //        // 遍历所有目标图元，寻找与延伸线段相交的点
        //        foreach (var e in eles)
        //        {
        //            //与目标图元的直线段求交
        //            foreach (var l in e.Lines)
        //            {
        //                //计算当前线段延伸后与目标直线的交点
        //                GFunc.GetLineExtendPoint(l, nearestLine, ptStart, vec, ref result);
        //            }

        //            //与目标图元的圆形求交
        //            foreach (var c in e.Circles)
        //            {
        //                //计算当前线段延伸后与目标圆的交点
        //                GFunc.GetLineExtendPoint(c, nearestLine, ptStart, vec, ref result);
        //            }
        //        }

        //        //应用延伸结果
        //        if (result != null)
        //        {
        //            if (lineToExtend.Count == 1) //单线段的情况控制点更新
        //            {
        //                //计算结果点到当前控制点的距离，确定替换哪个控制点
        //                var dis1 = GMath.Distance(ConPts[0], result);
        //                var dis2 = GMath.Distance(ConPts[2], result);

        //                //替换距离较近的控制点
        //                if (dis1 > dis2)
        //                {
        //                    DbPt ptMid = GMath.GetMidPt(ConPts[0], result); //计算中点
        //                    ptMid.PtType = 2; //设置中点类型为普通中点
        //                    ReSetConPts(new List<DbPt> { ConPts[0], ptMid, result }); //替换第二个控制点
        //                }
        //                else
        //                {
        //                    DbPt ptMid = GMath.GetMidPt(result, ConPts[2]); //计算中点
        //                    ptMid.PtType = 2; //设置中点类型为普通中点
        //                    ReSetConPts(new List<DbPt> { result, ptMid, ConPts[2] }); //替换第一个控制点
        //                }
        //            }
        //            else //多段线的情况
        //            {
        //                var conPts = GFunc_CW.PtListEleCopy(ConPts); //复制当前控制点列表

        //                if (index == 0) //延伸第一段线段
        //                {

        //                    //移除并替换第一个控制点
        //                    conPts.RemoveAt(0); //移除第一个控制点
        //                    conPts.Insert(0, result); //在开头插入延伸结果点

        //                    DbPt ptMid = GMath.GetMidPt(conPts[0], conPts[2]); //计算中点
        //                    ptMid.PtType = 2;

        //                    conPts.RemoveAt(1); //移除第一个控制点
        //                    conPts.Insert(1, ptMid); //在开头插入延伸结果点

        //                    ReSetConPts(conPts);
        //                    return true;
        //                }
        //                else //延伸最后一段线段
        //                {
        //                    DbPt ptMid = GMath.GetMidPt(conPts[conPts.Count - 3], result); //计算中点
        //                    ptMid.PtType = 2;

        //                    //移除并替换最后一个控制点
        //                    conPts.RemoveAt(conPts.Count - 1); //移除最后一个控制点
        //                    conPts.RemoveAt(conPts.Count - 1);
        //                    conPts.Add(ptMid);
        //                    conPts.Add(result);
        //                    ReSetConPts(conPts);
        //                    return true;
        //                }
        //            }
        //        }

        //        #endregion
        //    }
        //    else //处理弧线段延伸
        //    {
        //        #region 弧线段延伸逻辑

        //        //获取弧线的几何信息
        //        //返回逆时针圆弧
        //        DbLine arcToExtend = GMath.ArcAnLock(nearestLine);

        //        //计算弧线的圆心和半径
        //        var cenPt = GMath.GetArcCenter(arcToExtend.PtSt, arcToExtend.PtEnd, arcToExtend.PtMid);
        //        double radius = GMath.Distance(cenPt, arcToExtend.PtSt);

        //        //创建完整的圆形
        //        var circle = new DbCircle(cenPt, 2 * radius);

        //        //收集所有可能的延伸交点
        //        List<DbPt> extendPts = new List<DbPt>();

        //        // 遍历所有目标图元，寻找与延伸圆弧相交的点
        //        foreach (var e in eles)
        //        {
        //            //与目标图元的直线段求交
        //            foreach (var l in e.Lines)
        //            {
        //                GFunc.GetArcExtendPoint(l, circle, arcToExtend, extendPts);
        //            }
        //            //与目标图元的圆形求交
        //            foreach (var c in e.Circles)
        //            {
        //                GFunc.GetArcExtendPoint(c, circle, arcToExtend, extendPts);
        //            }
        //        }

        //        //处理弧线延伸结果
        //        if (extendPts.Count > 0)
        //        {
        //            //计算点击点在弧线上的投影（用于确定延伸方向）
        //            var footPt = GMath.ArcFootNearPt(arcToExtend, pt);

        //            //执行弧线延伸计算，获取延伸后的新弧线
        //            var result = GFunc.ArcExtend(null, arcToExtend, cenPt, radius, extendPts, footPt, null);


        //            if (result != null)
        //            {
        //                //保持弧线方向的一致性
        //                //如果弧线是顺时针，则将结果也设置为顺时针
        //                if (GMath.IfArcClockwise(nearestLine))
        //                    result = result.Reverse(); //逆时针弧线变为顺时针

        //                if (lineToExtend.Count == 1) //单弧线段情况
        //                {
        //                    //直接用延伸后的弧线重置控制点
        //                    ResetConPts(new List<DbLine> { result });
        //                    ActAndCal2D();
        //                }
        //                else
        //                {
        //                    //更新控制点：精确替换第一段弧线的控制点
        //                    var conPts = GFunc_CW.PtListEleCopy(ConPts);

        //                    if (index == 0) //延伸第一段弧线
        //                    {
        //                        //检查延伸是否有效（起点不能重叠）
        //                        if (result.PtSt.IfOverXY(nearestLine.PtSt)) return false;

        //                        //替换第一个弧线段的控制点
        //                        conPts[0] = result.PtSt.EleCopy(); //新的起点
        //                        conPts[1] = result.PtMid.EleCopy(); //新的弧线中点
        //                        conPts[1].PtType = 1; //确保弧线中点类型正确

        //                        ReSetConPts(conPts);
        //                        return true;
        //                    }
        //                    else
        //                    {
        //                        if (result.PtEnd.IfOverXY(nearestLine.PtEnd)) return false; //检查延伸是否有效

        //                        //替换最后一个弧线段的控制点
        //                        conPts[conPts.Count - 2] = result.PtMid.EleCopy(); //新的弧线中点
        //                        conPts[conPts.Count - 2].PtType = 1; //确保弧线中点类型正确
        //                        conPts[conPts.Count - 1] = result.PtEnd.EleCopy(); //新的终点
        //                        ReSetConPts(conPts);
        //                        return true;
        //                    }

        //                }
        //            }
        //        }
        //        #endregion
        //    }

        //    // 如果没有找到延伸点，返回false表示延伸失败
        //    return false;
        //}

        /// <summary>
        /// 图元裁剪
        /// </summary>
        public override void EleCut()
        {
            ActAndCal2D();
        }

        /// <summary>
        /// 与另一个图元发生倒角时的行为
        /// </summary>
        /// <param name="thisPt">倒角时当前图元的点击点</param>
        /// <param name="ele">参与倒角的另一个图元</param>
        /// <param name="pt">倒角时另一个图元的点击点</param>
        /// <param name="rad">圆角半径，0为倒直角</param>
        /// <returns>返回true：该图元可倒角，false：该图元不可倒角</returns>
        public override bool EleFilletWithEle(DbPt thisPt, DbElement ele, DbPt pt, double rad = 0)
        {
            return true;
        }

        /// <summary>
        /// 图元被偏移指定距离后的图元
        /// </summary>
        /// <_param name="dis">偏移距离</_param>
        /// <_param name="CurPt">动态函数过程中鼠标所在点，用以判断左偏还是右偏</_param>
        /// <returns>不支持偏移或在特定偏移距离下会偏移失败时返回null，否则返回偏移后的图元</returns>
        public override DbElement EleOffset(double dis, DbPt CurPt)
        {
            //// 获取钢矩管的基线用于裁切
            List<DbLine> baselines = this.LocationCurve;

            // 将基线的Z坐标归零，
            List<DbLine> baselines1 = new List<DbLine>();
            baselines.ForEach(l => baselines1.Add(GFunc_CW.ZeroZofLine(l)));
            CurPt.Z = 0; // 确保鼠标点击点的Z坐标为0

            // 获取与鼠标点击点最近的线段
            DbLine nearestLine = GFunc_CW.GetFootLineInLines(baselines, CurPt);

            int dirInt = GMath.LiArcDirection(nearestLine, CurPt); //获取偏移方向
            bool leftOrRight = dirInt == 1 ? true : false; //判断是左偏还是右偏
            List<DbPt> pts = GFunc.CopyPts(ConPts); //深度复制控制点列表
            var conPts = pts.Where(pt => pt.PtType != 2).ToList();
            var result = new List<DbPt>(); //存储偏移后的点
            if (this.Closed)
            {
                result = GMath.GetOffsetArcPts(conPts, dis, leftOrRight);
            }
            else
            {
                result = GMath.GetUnclosedOffsetArcPts(conPts, dis, leftOrRight); //获取非闭合钢矩管的偏移点
            }

            if (result == null || result.Count == 0) return null; //如果偏移失败，返回null

            SteelTubeSideType ele = new SteelTubeSideType(result, this.Direction, this.Closed)
            {
                _height = this.Height,
                _width = this.Width, //设置钢矩管的宽度和高度
                _thickness = this.Thickness, //设置钢矩管的厚度
                _material = this.Material, //设置钢矩管的材质
                _leftEdge = this.LeftEdge,
                _rightEdge = this.RightEdge, //设置钢矩管的左边缘和右边缘
                _surfaceTreatment = this.SurfaceTreatment, //设置钢矩管的表面处理
            };//复制当前钢矩管图元
            ele.ActAndCal2D(); //激活图元以更新显示
            return ele; //返回偏移后的钢矩管图元

        }

        #endregion

        #region 自定义函数
        /// <summary>
        /// 设置钢矩管的控制点，自带事务
        /// </summary>
        /// <_param name="pts"></_param>    
        public void ResetConPts(List<DbPt> pts)
        {
            if (pts.Count == 0) return;
            var preConPts = ConPts.Select(p => p.EleCopy()).ToList();
            using (new Transaction("修改铝单板控制点"))
            {
                TransManager.Instance().Push(_ =>
                {
                    ResetConPts(preConPts);
                }, "");
            }
            ConPts = pts;
            ActCutCal2D3D();
            EBDB.Instance.UIReDrawActiv();
        }

        /// <summary>
        /// 设置钢矩管的控制点，自带事务
        /// </summary>
        /// <_param name="ls"></_param>
        public void ResetConPts(List<DbLine> ls)
        {
            if (ls.Count == 0) return;
            var preConPts = ConPts.Select(p => p.EleCopy()).ToList();

            var pts = GMath.GetPtsOfLines(ls, false, true);

            using (new Transaction("修改铝单板控制线"))
            {
                TransManager.Instance().Push(_ =>
                {
                    ResetConPts(preConPts);
                }, "");

            }
            ConPts = pts;
        }

        #endregion

    }
