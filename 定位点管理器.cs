using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

/// <summary>
/// 定位点管理器 - 统一管理CAD平台中的所有定位点
/// 功能：显示控制、绑定管理、批量操作、事件通知
/// 实现定位点系统的核心控制逻辑
/// 优化：线程安全、性能优化、批量操作
/// </summary>
public class AnchorPointManager
{
    #region 单例模式
    private static AnchorPointManager _instance;
    private static readonly object _lock = new object();

    /// <summary>
    /// 获取管理器实例
    /// </summary>
    public static AnchorPointManager Instance
    {
        get
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new AnchorPointManager();
                    }
                }
            }
            return _instance;
        }
    }

    private AnchorPointManager()
    {
        _anchorPoints = new Dictionary<string, AnchorPoint>();
        _elementBindings = new Dictionary<string, List<string>>();
        _spatialIndex = new SpatialIndex<AnchorPoint>();
        _rwLock = new ReaderWriterLockSlim();
        _globalVisible = true;
        _globalEnabled = true;
    }
    #endregion

    #region 内部数据
    /// <summary>
    /// 所有定位点的集合 (UniqueId -> AnchorPoint)
    /// </summary>
    private Dictionary<string, AnchorPoint> _anchorPoints;

    /// <summary>
    /// 图元绑定关系 (ElementId -> List of AnchorPointIds)
    /// </summary>
    private Dictionary<string, List<string>> _elementBindings;

    /// <summary>
    /// 空间索引用于快速查找最近定位点
    /// </summary>
    private SpatialIndex<AnchorPoint> _spatialIndex;

    /// <summary>
    /// 读写锁用于线程安全
    /// </summary>
    private readonly ReaderWriterLockSlim _rwLock;

    /// <summary>
    /// 全局定位点可见性
    /// </summary>
    private bool _globalVisible;

    /// <summary>
    /// 全局定位点启用状态
    /// </summary>
    private bool _globalEnabled;
    #endregion

    #region 全局控制属性
    /// <summary>
    /// 全局定位点可见性
    /// </summary>
    public bool GlobalVisible
    {
        get { return _globalVisible; }
        set
        {
            if (_globalVisible != value)
            {
                _globalVisible = value;
                NotifyGlobalVisibilityChanged();
            }
        }
    }

    /// <summary>
    /// 全局定位点启用状态
    /// </summary>
    public bool GlobalEnabled
    {
        get { return _globalEnabled; }
        set
        {
            if (_globalEnabled != value)
            {
                _globalEnabled = value;
                NotifyGlobalEnabledChanged();
            }
        }
    }

    /// <summary>
    /// 获取当前管理的定位点数量
    /// </summary>
    public int Count => _anchorPoints.Count;
    #endregion

    #region 定位点管理
    /// <summary>
    /// 注册定位点到管理器
    /// </summary>
    /// <param name="anchorPoint">定位点实例</param>
    /// <returns>是否注册成功</returns>
    public bool RegisterAnchorPoint(AnchorPoint anchorPoint)
    {
        if (anchorPoint == null || string.IsNullOrEmpty(anchorPoint.UniqueId))
            return false;

        _rwLock.EnterWriteLock();
        try
        {
            if (_anchorPoints.ContainsKey(anchorPoint.UniqueId))
                return false; // 已存在

            _anchorPoints[anchorPoint.UniqueId] = anchorPoint;
            _spatialIndex.Add(anchorPoint, anchorPoint.Position);
            
            OnAnchorPointRegistered(anchorPoint);
            return true;
        }
        finally
        {
            _rwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 从管理器注销定位点
    /// </summary>
    /// <param name="anchorPointId">定位点ID</param>
    /// <returns>是否注销成功</returns>
    public bool UnregisterAnchorPoint(string anchorPointId)
    {
        if (string.IsNullOrEmpty(anchorPointId))
            return false;

        _rwLock.EnterWriteLock();
        try
        {
            if (!_anchorPoints.ContainsKey(anchorPointId))
                return false;

            AnchorPoint anchorPoint = _anchorPoints[anchorPointId];
            
            // 清除所有相关绑定
            ClearAnchorPointBindings(anchorPointId);
            
            // 从空间索引中移除
            _spatialIndex.Remove(anchorPoint);
            
            _anchorPoints.Remove(anchorPointId);
            OnAnchorPointUnregistered(anchorPoint);
            return true;
        }
        finally
        {
            _rwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 获取定位点（线程安全）
    /// </summary>
    /// <param name="anchorPointId">定位点ID</param>
    /// <returns>定位点实例，不存在则返回null</returns>
    public AnchorPoint GetAnchorPoint(string anchorPointId)
    {
        if (string.IsNullOrEmpty(anchorPointId))
            return null;

        _rwLock.EnterReadLock();
        try
        {
            _anchorPoints.TryGetValue(anchorPointId, out AnchorPoint anchorPoint);
            return anchorPoint;
        }
        finally
        {
            _rwLock.ExitReadLock();
        }
    }

    /// <summary>
    /// 获取所有定位点
    /// </summary>
    /// <returns>所有定位点的列表</returns>
    public List<AnchorPoint> GetAllAnchorPoints()
    {
        return _anchorPoints.Values.ToList();
    }

    /// <summary>
    /// 根据类型获取定位点
    /// </summary>
    /// <param name="anchorType">定位点类型</param>
    /// <returns>指定类型的定位点列表</returns>
    public List<AnchorPoint> GetAnchorPointsByType(AnchorPoint.AnchorType anchorType)
    {
        return _anchorPoints.Values.Where(ap => ap.AnchorType == anchorType).ToList();
    }

    /// <summary>
    /// 根据位置查找最近的定位点（优化版本使用空间索引）
    /// </summary>
    /// <param name="position">查找位置</param>
    /// <param name="maxDistance">最大搜索距离</param>
    /// <returns>最近的定位点，超出距离返回null</returns>
    public AnchorPoint FindNearestAnchorPoint(DbPt position, double maxDistance = 50.0)
    {
        _rwLock.EnterReadLock();
        try
        {
            return _spatialIndex.FindNearest(position, maxDistance, 
                ap => ap.IsEnabled && _globalEnabled);
        }
        finally
        {
            _rwLock.ExitReadLock();
        }
    }
    #endregion

    #region 绑定管理
    /// <summary>
    /// 建立图元与定位点的绑定关系
    /// </summary>
    /// <param name="elementId">图元ID</param>
    /// <param name="anchorPointId">定位点ID</param>
    /// <returns>是否绑定成功</returns>
    public bool BindElementToAnchor(string elementId, string anchorPointId)
    {
        if (string.IsNullOrEmpty(elementId) || string.IsNullOrEmpty(anchorPointId))
            return false;

        if (!_anchorPoints.ContainsKey(anchorPointId))
            return false;

        // 添加到图元绑定关系
        if (!_elementBindings.ContainsKey(elementId))
        {
            _elementBindings[elementId] = new List<string>();
        }

        if (!_elementBindings[elementId].Contains(anchorPointId))
        {
            _elementBindings[elementId].Add(anchorPointId);
        }

        // 添加到定位点的绑定列表
        _anchorPoints[anchorPointId].AddBinding(elementId);

        OnBindingCreated(elementId, anchorPointId);
        return true;
    }

    /// <summary>
    /// 解除图元与定位点的绑定关系
    /// </summary>
    /// <param name="elementId">图元ID</param>
    /// <param name="anchorPointId">定位点ID</param>
    /// <returns>是否解绑成功</returns>
    public bool UnbindElementFromAnchor(string elementId, string anchorPointId)
    {
        if (string.IsNullOrEmpty(elementId) || string.IsNullOrEmpty(anchorPointId))
            return false;

        bool removed = false;

        // 从图元绑定关系中移除
        if (_elementBindings.ContainsKey(elementId))
        {
            removed = _elementBindings[elementId].Remove(anchorPointId);
            if (_elementBindings[elementId].Count == 0)
            {
                _elementBindings.Remove(elementId);
            }
        }

        // 从定位点的绑定列表中移除
        if (_anchorPoints.ContainsKey(anchorPointId))
        {
            _anchorPoints[anchorPointId].RemoveBinding(elementId);
        }

        if (removed)
        {
            OnBindingRemoved(elementId, anchorPointId);
        }

        return removed;
    }

    /// <summary>
    /// 获取图元绑定的所有定位点
    /// </summary>
    /// <param name="elementId">图元ID</param>
    /// <returns>绑定的定位点列表</returns>
    public List<AnchorPoint> GetElementBoundAnchors(string elementId)
    {
        if (string.IsNullOrEmpty(elementId) || !_elementBindings.ContainsKey(elementId))
            return new List<AnchorPoint>();

        List<AnchorPoint> boundAnchors = new List<AnchorPoint>();
        foreach (string anchorId in _elementBindings[elementId])
        {
            if (_anchorPoints.ContainsKey(anchorId))
            {
                boundAnchors.Add(_anchorPoints[anchorId]);
            }
        }

        return boundAnchors;
    }

    /// <summary>
    /// 获取定位点绑定的所有图元ID
    /// </summary>
    /// <param name="anchorPointId">定位点ID</param>
    /// <returns>绑定的图元ID列表</returns>
    public List<string> GetAnchorBoundElements(string anchorPointId)
    {
        if (string.IsNullOrEmpty(anchorPointId) || !_anchorPoints.ContainsKey(anchorPointId))
            return new List<string>();

        return _anchorPoints[anchorPointId].GetBoundElementIds();
    }

    /// <summary>
    /// 清除定位点的所有绑定关系
    /// </summary>
    /// <param name="anchorPointId">定位点ID</param>
    private void ClearAnchorPointBindings(string anchorPointId)
    {
        if (string.IsNullOrEmpty(anchorPointId))
            return;

        // 清除图元侧的绑定关系
        List<string> elementsToUpdate = new List<string>();
        foreach (var kvp in _elementBindings)
        {
            if (kvp.Value.Remove(anchorPointId))
            {
                elementsToUpdate.Add(kvp.Key);
            }
        }

        // 移除空的绑定条目
        foreach (string elementId in elementsToUpdate)
        {
            if (_elementBindings[elementId].Count == 0)
            {
                _elementBindings.Remove(elementId);
            }
        }

        // 清除定位点侧的绑定关系
        if (_anchorPoints.ContainsKey(anchorPointId))
        {
            _anchorPoints[anchorPointId].ClearBindings();
        }
    }

    /// <summary>
    /// 清除图元的所有绑定关系
    /// </summary>
    /// <param name="elementId">图元ID</param>
    public void ClearElementBindings(string elementId)
    {
        if (string.IsNullOrEmpty(elementId) || !_elementBindings.ContainsKey(elementId))
            return;

        List<string> anchorIds = new List<string>(_elementBindings[elementId]);
        foreach (string anchorId in anchorIds)
        {
            UnbindElementFromAnchor(elementId, anchorId);
        }
    }
    #endregion

    #region 批量操作
    /// <summary>
    /// 批量注册定位点
    /// </summary>
    /// <param name="anchorPoints">定位点集合</param>
    /// <returns>成功注册的数量</returns>
    public int RegisterAnchorPoints(IEnumerable<AnchorPoint> anchorPoints)
    {
        if (anchorPoints == null) return 0;
        
        int successCount = 0;
        _rwLock.EnterWriteLock();
        try
        {
            foreach (var anchor in anchorPoints)
            {
                if (RegisterAnchorPointInternal(anchor))
                    successCount++;
            }
        }
        finally
        {
            _rwLock.ExitWriteLock();
        }
        
        return successCount;
    }

    /// <summary>
    /// 内部注册方法（无锁版本）
    /// </summary>
    private bool RegisterAnchorPointInternal(AnchorPoint anchorPoint)
    {
        if (anchorPoint == null || string.IsNullOrEmpty(anchorPoint.UniqueId))
            return false;

        if (_anchorPoints.ContainsKey(anchorPoint.UniqueId))
            return false; // 已存在

        _anchorPoints[anchorPoint.UniqueId] = anchorPoint;
        _spatialIndex.Add(anchorPoint, anchorPoint.Position);
        
        OnAnchorPointRegistered(anchorPoint);
        return true;
    }

    /// <summary>
    /// 设置所有定位点的可见性
    /// </summary>
    /// <param name="visible">可见性</param>
    public void SetAllVisible(bool visible)
    {
        _rwLock.EnterWriteLock();
        try
        {
            foreach (var anchorPoint in _anchorPoints.Values)
            {
                // 注意：这里不直接设置定位点的可见性，而是通过图层管理
                // 具体实现取决于CAD平台的图层系统
            }
            GlobalVisible = visible;
        }
        finally
        {
            _rwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 设置所有定位点的启用状态
    /// </summary>
    /// <param name="enabled">启用状态</param>
    public void SetAllEnabled(bool enabled)
    {
        foreach (var anchorPoint in _anchorPoints.Values)
        {
            anchorPoint.IsEnabled = enabled;
        }
        GlobalEnabled = enabled;
    }

    /// <summary>
    /// 根据类型设置定位点的启用状态
    /// </summary>
    /// <param name="anchorType">定位点类型</param>
    /// <param name="enabled">启用状态</param>
    public void SetTypeEnabled(AnchorPoint.AnchorType anchorType, bool enabled)
    {
        foreach (var anchorPoint in _anchorPoints.Values)
        {
            if (anchorPoint.AnchorType == anchorType)
            {
                anchorPoint.IsEnabled = enabled;
            }
        }
    }

    /// <summary>
    /// 清除所有定位点
    /// </summary>
    public void ClearAll()
    {
        List<string> anchorIds = new List<string>(_anchorPoints.Keys);
        foreach (string anchorId in anchorIds)
        {
            UnregisterAnchorPoint(anchorId);
        }

        _anchorPoints.Clear();
        _elementBindings.Clear();
    }
    #endregion

    #region 查询和统计
    /// <summary>
    /// 获取绑定统计信息
    /// </summary>
    /// <returns>绑定统计字典 (AnchorType -> Count)</returns>
    public Dictionary<AnchorPoint.AnchorType, int> GetBindingStatistics()
    {
        var stats = new Dictionary<AnchorPoint.AnchorType, int>();
        
        foreach (AnchorPoint.AnchorType type in Enum.GetValues(typeof(AnchorPoint.AnchorType)))
        {
            stats[type] = 0;
        }

        foreach (var anchorPoint in _anchorPoints.Values)
        {
            if (anchorPoint.GetBoundElementIds().Count > 0)
            {
                stats[anchorPoint.AnchorType]++;
            }
        }

        return stats;
    }

    /// <summary>
    /// 检查是否存在无效绑定（图元已删除但绑定关系仍存在）
    /// </summary>
    /// <param name="validElementIds">当前有效的图元ID集合</param>
    /// <returns>无效绑定的数量</returns>
    public int ValidateBindings(HashSet<string> validElementIds)
    {
        int invalidCount = 0;
        List<string> elementsToRemove = new List<string>();

        foreach (string elementId in _elementBindings.Keys)
        {
            if (!validElementIds.Contains(elementId))
            {
                elementsToRemove.Add(elementId);
                invalidCount++;
            }
        }

        // 清除无效绑定
        foreach (string elementId in elementsToRemove)
        {
            ClearElementBindings(elementId);
        }

        return invalidCount;
    }
    #endregion

    #region 事件通知
    /// <summary>
    /// 定位点注册事件
    /// </summary>
    public event Action<AnchorPoint> AnchorPointRegistered;

    /// <summary>
    /// 定位点注销事件
    /// </summary>
    public event Action<AnchorPoint> AnchorPointUnregistered;

    /// <summary>
    /// 绑定创建事件
    /// </summary>
    public event Action<string, string> BindingCreated;

    /// <summary>
    /// 绑定移除事件
    /// </summary>
    public event Action<string, string> BindingRemoved;

    /// <summary>
    /// 全局可见性改变事件
    /// </summary>
    public event Action<bool> GlobalVisibilityChanged;

    /// <summary>
    /// 全局启用状态改变事件
    /// </summary>
    public event Action<bool> GlobalEnabledChanged;

    private void OnAnchorPointRegistered(AnchorPoint anchorPoint)
    {
        AnchorPointRegistered?.Invoke(anchorPoint);
    }

    private void OnAnchorPointUnregistered(AnchorPoint anchorPoint)
    {
        AnchorPointUnregistered?.Invoke(anchorPoint);
    }

    private void OnBindingCreated(string elementId, string anchorPointId)
    {
        BindingCreated?.Invoke(elementId, anchorPointId);
    }

    private void OnBindingRemoved(string elementId, string anchorPointId)
    {
        BindingRemoved?.Invoke(elementId, anchorPointId);
    }

    private void NotifyGlobalVisibilityChanged()
    {
        GlobalVisibilityChanged?.Invoke(_globalVisible);
    }

    private void NotifyGlobalEnabledChanged()
    {
        GlobalEnabledChanged?.Invoke(_globalEnabled);
    }
    #endregion

    #region 实用方法
    /// <summary>
    /// 创建定位点并自动注册
    /// </summary>
    /// <param name="position">位置</param>
    /// <param name="anchorType">类型</param>
    /// <param name="direction">方向</param>
    /// <param name="displaySize">显示大小</param>
    /// <returns>创建的定位点</returns>
    public AnchorPoint CreateAnchorPoint(DbPt position, AnchorPoint.AnchorType anchorType = AnchorPoint.AnchorType.Insert,
                                       double direction = 0.0, double displaySize = 8.0)
    {
        AnchorPoint anchorPoint = new AnchorPoint(position, anchorType, direction, displaySize);
        anchorPoint.UniqueId = Guid.NewGuid().ToString();
        
        if (RegisterAnchorPoint(anchorPoint))
        {
            return anchorPoint;
        }
        
        return null;
    }

    /// <summary>
    /// 在指定位置智能创建定位点（避免重复）
    /// </summary>
    /// <param name="position">位置</param>
    /// <param name="anchorType">类型</param>
    /// <param name="tolerance">容差范围</param>
    /// <returns>创建或已存在的定位点</returns>
    public AnchorPoint GetOrCreateAnchorPoint(DbPt position, AnchorPoint.AnchorType anchorType, double tolerance = 5.0)
    {
        AnchorPoint existing = FindNearestAnchorPoint(position, tolerance);
        if (existing != null && existing.AnchorType == anchorType)
        {
            return existing;
        }

        return CreateAnchorPoint(position, anchorType);
    }
    #endregion
}

/// <summary>
/// 简化的空间索引实现
/// 用于快速查找最近的定位点
/// </summary>
/// <typeparam name="T">索引对象类型</typeparam>
public class SpatialIndex<T> where T : class
{
    private readonly Dictionary<T, DbPt> _items = new Dictionary<T, DbPt>();

    /// <summary>
    /// 添加项目到索引
    /// </summary>
    /// <param name="item">项目</param>
    /// <param name="position">位置</param>
    public void Add(T item, DbPt position)
    {
        _items[item] = position;
    }

    /// <summary>
    /// 从索引中移除项目
    /// </summary>
    /// <param name="item">项目</param>
    public void Remove(T item)
    {
        _items.Remove(item);
    }

    /// <summary>
    /// 查找最近的项目
    /// </summary>
    /// <param name="position">查找位置</param>
    /// <param name="maxDistance">最大距离</param>
    /// <param name="filter">过滤条件</param>
    /// <returns>最近的项目</returns>
    public T FindNearest(DbPt position, double maxDistance, Func<T, bool> filter = null)
    {
        T nearest = null;
        double minDistance = maxDistance;

        foreach (var kvp in _items)
        {
            T item = kvp.Key;
            DbPt itemPos = kvp.Value;

            // 应用过滤条件
            if (filter != null && !filter(item))
                continue;

            double distance = Math.Sqrt(
                Math.Pow(itemPos.X - position.X, 2) +
                Math.Pow(itemPos.Y - position.Y, 2)
            );

            if (distance < minDistance)
            {
                minDistance = distance;
                nearest = item;
            }
        }

        return nearest;
    }

    /// <summary>
    /// 更新项目位置
    /// </summary>
    /// <param name="item">项目</param>
    /// <param name="newPosition">新位置</param>
    public void UpdatePosition(T item, DbPt newPosition)
    {
        if (_items.ContainsKey(item))
        {
            _items[item] = newPosition;
        }
    }

    /// <summary>
    /// 清空索引
    /// </summary>
    public void Clear()
    {
        _items.Clear();
    }
}