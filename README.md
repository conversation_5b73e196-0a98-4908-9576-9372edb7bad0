# 矩管定位点系统 - 架构设计文档

## 项目概述

本项目实现了一个完整的矩管定位点系统，为CAD图元平台提供专业的矩管图元和定位点功能。系统采用独立图元架构，符合平台"一个图元一个独立实体"的设计原则。

## 架构设计

### 核心组件

1. **矩管图元 (SteelTubePlanD)** - 专为独立架构设计的纯矩管图元，专注于矩管本身的几何计算和显示
2. **定位点图元 (TubeLocatorPoint)** - 独立的定位点图元，提供位置控制和智能拉伸功能
3. **绑定管理器 (TubeLocatorBindingManager)** - 管理矩管与定位点之间的关联关系
4. **钢矩管侧视图元 (SteelTubeSideType)** - 专为侧视图设计的钢矩管图元，支持动态绘制和精确偏移

### 设计原则

- **独立性**: 每个图元都是独立的实体，可以单独创建、编辑和删除
- **松耦合**: 矩管和定位点通过绑定系统关联，而不是直接耦合
- **可扩展**: 支持多种定位点类型，易于扩展新功能
- **线程安全**: 绑定管理器采用线程安全设计，支持并发访问
- **精确偏移**: 确保内壁线和外壁线与基准线平行且不相交

## 文件结构

```
矩管定位点系统/
├── 矩管方案D.cs                     # 独立架构矩管图元实现（推荐）
├── 矩管方案C.cs                     # 原内置定位点版本（已重构为纯矩管）
├── 矩管专用定位点图元.cs            # 独立定位点图元实现
├── 矩管定位点绑定管理器.cs          # 绑定关系管理器
├── 定位点图元.cs                    # 通用定位点图元基类
├── 矩管定位点绑定.cs                # 复杂绑定逻辑实现
├── 定位点管理器.cs                  # 定位点生命周期管理
├── 钢矩管.CS                        # 钢矩管侧视图元实现
├── 钢矩管实现命令.CS                # 钢矩管创建命令实现
├── 使用示例.cs                      # 使用示例和测试代码
└── README.md                        # 本文档
```

## 功能特性

### 矩管图元功能

- **几何参数控制**: 宽度、高度、壁厚、旋转角度
- **自动圆角计算**: 根据壁厚自动计算合适的圆角半径
- **偏移控制**: 支持左右上下偏移，灵活控制插入点位置
- **变换支持**: 支持移动、旋转、镜像等几何变换
- **智能拉伸**: 通过边中点控制点实现智能拉伸

### 钢矩管侧视图功能

- **动态绘制**: 支持实时预览和动态绘制
- **精确偏移**: 内壁线1、内壁线2和外壁线与基准线平行且不相交
- **多线段支持**: 支持直线段和弧线段的混合绘制
- **端部封口**: 支持起始端和终止端的封口处理
- **表面处理**: 支持多种表面处理方式设置

### 定位点图元功能

- **多种类型**: 支持上中、下中、左中、右中、几何中心等定位点类型
- **位置控制**: 精确的位置控制和偏移设置
- **智能拉伸**: 根据定位点类型执行对应的智能拉伸逻辑
- **可视化**: 3mm直径圆形+3.5mm十字中心线的标准显示
- **状态管理**: 支持启用/禁用、显示/隐藏状态控制

### 绑定系统功能

- **自动同步**: 矩管和定位点位置自动同步
- **智能拉伸**: 拖拽定位点时自动调整矩管尺寸
- **关系管理**: 完整的绑定关系创建、删除、查询功能
- **线程安全**: 支持多线程环境下的并发访问

## 使用方法

### 基本使用

```csharp
// 1. 创建矩管图元
var tube = new SteelTubePlanD(
    insertPt: new DbPt(0, 0),
    width: 60,
    height: 120,
    thickness: 3
);

// 2. 创建定位点图元
var locator = new TubeLocatorPoint(
    position: new DbPt(0, -90),
    locatorType: TubeLocatorPoint.TubeLocatorType.BottomCenter,
    offsetY: 30
);

// 3. 建立绑定关系
TubeLocatorBindingManager.CreateBinding(
    tubeId: tube.UniqueId,
    locatorId: locator.UniqueId,
    locatorType: locator.LocatorType
);

// 4. 添加到视图
EbDb.ActivVi2D.GView.AddElement(tube);
EbDb.ActivVi2D.GView.AddElement(locator);
```

### 钢矩管侧视图使用

```csharp
// 1. 创建钢矩管侧视图
var steelTube = new SteelTubeSideType(
    pts: vertices,
    direction: true,
    closed: false
);

// 2. 设置参数
steelTube.Thickness = 3.0;      // 壁厚
steelTube.Height = 120.0;       // 高度
steelTube.Width = 60.0;         // 宽度
steelTube.LeftEdge = false;     // 起始端封口
steelTube.RightEdge = false;    // 终止端封口

// 3. 激活并添加到视图
steelTube.ActAndCal2D();
view.GView.AddElement(steelTube);
```

### 高级功能

```csharp
// 智能拉伸
TubeLocatorBindingManager.PerformSmartStretch(
    locatorId: locator.UniqueId,
    stretchVector: new DbPt(0, 20)
);

// 位置同步
TubeLocatorBindingManager.SyncLocatorPosition(
    tubeId: tube.UniqueId,
    locatorId: locator.UniqueId,
    locatorType: locator.LocatorType
);

// 查询绑定关系
var locators = TubeLocatorBindingManager.GetTubeLocators(tube.UniqueId);
var boundTube = TubeLocatorBindingManager.GetLocatorTube(locator.UniqueId);
```

## 定位点类型说明

| 类型 | 描述 | 用途 |
|------|------|------|
| TopCenter | 上边中点 | 控制矩管上边位置，支持垂直拉伸 |
| BottomCenter | 下边中点 | 控制矩管下边位置，支持垂直拉伸 |
| LeftCenter | 左边中点 | 控制矩管左边位置，支持水平拉伸 |
| RightCenter | 右边中点 | 控制矩管右边位置，支持水平拉伸 |
| GeometryCenter | 几何中心 | 控制矩管整体位置，支持整体移动 |

## 智能拉伸逻辑

- **上边中点**: 向上拉伸增加高度，向下拉伸减少高度
- **下边中点**: 向下拉伸增加高度，向上拉伸减少高度  
- **左边中点**: 向左拉伸增加宽度，向右拉伸减少宽度
- **右边中点**: 向右拉伸增加宽度，向左拉伸减少宽度
- **几何中心**: 整体移动，不改变尺寸

## 技术特点

### 性能优化

- **智能控制点管理**: 只在必要时重新计算控制点
- **缓存机制**: 绑定管理器使用缓存提高查询性能
- **延迟计算**: 几何计算采用延迟计算策略

### 错误处理

- **异常捕获**: 完善的异常处理机制
- **日志记录**: 详细的错误日志和调试信息
- **容错设计**: 在异常情况下保持系统稳定

### 扩展性

- **插件式架构**: 易于扩展新的定位点类型
- **接口设计**: 清晰的接口定义，便于集成
- **配置化**: 支持参数配置和自定义设置

## 开发指南

### 添加新的定位点类型

1. 在 `TubeLocatorType` 枚举中添加新类型
2. 在 `CalculateLocatorPosition` 方法中添加位置计算逻辑
3. 在 `PerformSmartStretch` 方法中添加拉伸逻辑
4. 更新相关文档和测试用例

### 自定义绑定逻辑

1. 继承或扩展 `TubeLocatorBindingManager` 类
2. 重写相关的同步和拉伸方法
3. 实现自定义的位置计算算法
4. 注册新的绑定类型

## 测试用例

项目包含完整的测试用例，覆盖以下场景：

- 矩管图元的基本功能测试
- 定位点图元的显示和交互测试
- 绑定关系的创建和管理测试
- 智能拉伸功能的准确性测试
- 并发访问的线程安全测试

## 最近更新

### 2024.12.19 - 钢矩管侧视图修复

**修复内容：**
1. **偏移计算修复**：重新修正了内壁线1、内壁线2和外壁线的偏移方向和距离计算
   - 内壁线1：向内偏移thickness距离（相对于direction的反方向，使用`!_direction`）
   - 内壁线2：向外偏移thickness距离（相对于direction方向，使用`_direction`）
   - 外壁线：向外偏移height距离（相对于direction方向，使用`_direction`）

2. **端部连接线改进**：修复了端部连接线的绘制逻辑
   - 从基准线到内壁线1的连接线
   - 从内壁线1到内壁线2的连接线
   - 从内壁线2到外壁线的连接线

3. **动态预览优化**：修复了动态预览中线条相交的问题
   - 确保预览效果与最终结果一致
   - 优化了预览颜色设置逻辑

4. **内壁线绘制改进**：重构了`DrawInnerLines`方法
   - 增强了弧线段的处理逻辑
   - 改进了封口线交点的计算
   - 确保内壁线不与其他线条相交

5. **代码优化**：
   - 统一了注释和命名规范
   - 改进了错误处理机制
   - 增强了代码可读性

**解决的问题：**
- 动态预览中外轮廓线和内壁线2与基准线相交
- 最终生成的矩管外壁线、内壁线2与基准线不齐平
- 内壁线绘制逻辑不完善导致的显示问题
- 端部连接线缺失或不正确的问题

## 常见问题

### Q: 为什么要将定位点设计为独立图元？

A: 这样设计符合CAD平台的架构原则，每个图元都是独立的实体，可以单独选择、编辑和删除。同时也便于扩展和维护。

### Q: 如何处理矩管删除时的定位点清理？

A: 当矩管被删除时，应该调用 `TubeLocatorBindingManager.ClearTubeBindings()` 方法清理相关的绑定关系，并根据需要删除或保留定位点图元。

### Q: 定位点的显示样式可以自定义吗？

A: 可以。通过修改 `TubeLocatorPoint` 类中的 `GenerateLocatorGraphics()` 方法，可以自定义定位点的显示样式、颜色和大小。

### Q: 如何实现多个定位点同时控制一个矩管？

A: 绑定管理器支持一个矩管绑定多个定位点。每个定位点都可以独立控制矩管的不同方面，如位置、尺寸等。

### Q: 钢矩管侧视图中的内壁线为什么会相交？

A: 这通常是由于偏移计算方向错误或距离不正确导致的。最新版本已经修复了这个问题，确保内壁线1向内偏移，内壁线2和外壁线向外偏移。

### Q: 为什么钢矩管参照铝单板实现却有问题？

A: 虽然钢矩管参照了铝单板的实现，但关键在于偏移方法的选择。铝单板正确地区分了闭合和非闭合情况，分别使用`GetOffsetArcPts`和`GetUnclosedOffsetArcPts`方法。而钢矩管最初统一使用了`GetOffsetArcPts`方法，导致非闭合钢矩管的偏移计算错误。最新版本已修复此问题。

## 版本历史

- **v1.0**: 初始版本，实现基本的矩管和定位点功能
- **v1.1**: 添加智能拉伸功能和绑定系统
- **v1.2**: 优化性能，增加线程安全支持
- **v1.3**: 完善错误处理和日志记录
- **v1.4**: 钢矩管侧视图修复，解决线条相交和不齐平问题
- **v1.5**: 钢矩管偏移方法修复，参照铝单板正确实现，区分闭合和非闭合偏移方法

## 贡献指南

欢迎提交问题报告和功能建议。在提交代码前，请确保：

1. 遵循现有的代码风格和命名规范
2. 添加必要的注释和文档
3. 编写相应的测试用例
4. 更新相关的文档

## 许可证

本项目采用 MIT 许可证。详细信息请参阅 LICENSE 文件。 