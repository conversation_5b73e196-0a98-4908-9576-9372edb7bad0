using System;
using System.Collections.Generic;
using System.Linq;
using EBDB;
using GuiDB;

namespace CurtainWall
{
    public class LineToSteelTubeCmd : IExternalCmd
    {
        public string Describe()
        {
            return "线变钢矩管";
        }

        public Result Execute(EBDB EbDb)
        {
            if (ArcGFunc.IsExitDraw(EbDb))
            {
                return Result.Cancelled;
            }

            // 获取矩管偏移距离
            double offsetDistance = 0;
            string offsetInput = EbDb.UICmdLine("请输入矩管偏移距离(mm):", "0", "");
            if (!double.TryParse(offsetInput, out offsetDistance))
            {
                offsetDistance = 0;
            }

            // 选择要转换的图元
            var eles = EbDb.UIBoxSelect("选择要生成钢矩管的直线、多义线、铝单板：", new LineToSteelTube_Filter());
            
            if (eles.Count == 0)
            {
                return Result.Failed;
            }

            List<SteelTubeSideType> steelTubes = new List<SteelTubeSideType>();

            foreach (var ele in eles)
            {
                if (ele is Line line)
                {
                    // 处理直线
                    if (line.Lines[0].GetLong() > 50) // 最小长度50mm
                    {
                        var steelTube = CreateSteelTubeFromLine(line.Lines[0], offsetDistance);
                        if (steelTube != null) { steelTubes.Add(steelTube); }
                    }
                }
                else if (ele is Polyline polyline)
                {
                    // 处理多义线
                    var tubes = CreateSteelTubeFromPolyline(polyline, offsetDistance);
                    steelTubes.AddRange(tubes);
                }
                else if (ele is AluminumPanel aluminum)
                {
                    // 处理铝单板基线
                    var tubes = CreateSteelTubeFromAluminum(aluminum, offsetDistance);
                    steelTubes.AddRange(tubes);
                }
            }

            // 添加到视图
            if (steelTubes.Count > 0)
            {
                using (Transaction transaction = new Transaction("线变钢矩管"))
                {
                    EbDb.ActivView2D.GView.AddElement(steelTubes);
                }
            }

            return Result.Succeeded;
        }

        public string Name()
        {
            return "线变钢矩管";
        }

        /// <summary>
        /// 从单条线段创建钢矩管
        /// </summary>
        private SteelTubeSideType CreateSteelTubeFromLine(DbLine line, double offsetDistance)
        {
            List<DbPt> pts = new List<DbPt> { line.PtSt, line.PtEnd };
            
            // 应用偏移
            if (!GMath.IsEqual(offsetDistance, 0))
            {
                pts = ApplyOffset(pts, offsetDistance, line);
            }

            var steelTube = new SteelTubeSideType(pts, true) // true表示绘制方向
            {
                Height = 120,
                Width = 60,
                Thickness = 3,
                LeftEdge = false,
                RightEdge = false
            };

            return steelTube;
        }

        /// <summary>
        /// 从多义线创建钢矩管
        /// </summary>
        private List<SteelTubeSideType> CreateSteelTubeFromPolyline(Polyline polyline, double offsetDistance)
        {
            List<SteelTubeSideType> tubes = new List<SteelTubeSideType>();

            // 应用偏移
            Polyline offsetPolyline = polyline;
            if (!GMath.IsEqual(offsetDistance, 0))
            {
                DbLine refLine = GMath.LineArcOffset(polyline.Lines[0], true, 200);
                offsetPolyline = ArcGFunc.OffsetPolyLine(polyline, offsetDistance, refLine.PtMid);
            }

            if (offsetPolyline == null) return tubes;

            if (offsetPolyline.Lines.Count == 1)
            {
                // 单线段
                if (offsetPolyline.Lines[0].GetLong() > 50)
                {
                    var tube = CreateSteelTubeFromLine(offsetPolyline.Lines[0], 0); // 已经偏移过了
                    if (tube != null) tubes.Add(tube);
                }
            }
            else
            {
                // 多线段 - 每段创建一个钢矩管
                foreach (var line in offsetPolyline.Lines)
                {
                    if (line.GetLong() > 50)
                    {
                        var tube = CreateSteelTubeFromLine(line, 0); // 已经偏移过了
                        if (tube != null) tubes.Add(tube);
                    }
                }
            }

            return tubes;
        }

        /// <summary>
        /// 从铝单板创建钢矩管
        /// </summary>
        private List<SteelTubeSideType> CreateSteelTubeFromAluminum(AluminumPanel aluminum, double offsetDistance)
        {
            List<SteelTubeSideType> tubes = new List<SteelTubeSideType>();

            // 获取铝单板的基线
            var locationCurves = aluminum.LocationCurve;
            if (locationCurves == null || locationCurves.Count == 0) return tubes;

            foreach (var line in locationCurves)
            {
                if (line.GetLong() > 50)
                {
                    var tube = CreateSteelTubeFromLine(line, offsetDistance);
                    if (tube != null) tubes.Add(tube);
                }
            }

            return tubes;
        }

        /// <summary>
        /// 应用偏移到点集
        /// </summary>
        private List<DbPt> ApplyOffset(List<DbPt> pts, double offsetDistance, DbLine refLine)
        {
            if (GMath.IsEqual(offsetDistance, 0)) return pts;

            // 计算偏移方向
            DbPt midPt = refLine.PtMid;
            DbPt offsetPt = midPt.Move(offsetDistance * GMath.GetNormalDir(refLine.GetDir()));
            
            // 简单的平移偏移
            List<DbPt> offsetPts = new List<DbPt>();
            DbPt moveVector = offsetPt.Subtract(midPt);
            
            foreach (var pt in pts)
            {
                offsetPts.Add(pt.Move(moveVector.X, moveVector.Y));
            }

            return offsetPts;
        }
    }

    /// <summary>
    /// 线转钢矩管的图元过滤器
    /// </summary>
    public class LineToSteelTube_Filter : IEleFilter
    {
        public bool IsMatch(DbElement ele)
        {
            return ele is Line || ele is Polyline || ele is AluminumPanel;
        }
    }
}