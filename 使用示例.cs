using System;
using System.Collections.Generic;

/// <summary>
/// 矩管定位点系统使用示例
/// 展示如何创建矩管、定位点并建立绑定关系
/// </summary>
public class TubeAnchorSystemDemo
{
    /// <summary>
    /// 基本使用示例：创建矩管和定位点，建立插入点绑定
    /// </summary>
    public static void BasicUsageDemo()
    {
        // 1. 创建矩管
        DbPt insertPoint = new DbPt(100, 100);
        SteelTube tube = new SteelTube(insertPoint, 80, 120, 3.0, 0.0);
        
        // 2. 启用定位点功能
        tube.EnableAnchorPoints = true;
        
        // 3. 设置定位点位置和参数
        tube.AnchorPosition = TubeAnchorBinding.RelativePosition.BottomCenter;
        tube.EnableSmartStretching = true;
        
        // 4. 矩管会自动创建默认定位点并建立绑定关系
        AnchorPoint primaryAnchor = tube.GetPrimaryAnchor();
        if (primaryAnchor != null)
        {
            Console.WriteLine($"主定位点已创建，位置: ({primaryAnchor.Position.X}, {primaryAnchor.Position.Y})");
        }
    }

    /// <summary>
    /// 高级使用示例：手动创建定位点并建立多种绑定关系
    /// </summary>
    public static void AdvancedUsageDemo()
    {
        // 1. 创建矩管
        DbPt insertPoint = new DbPt(200, 200);
        SteelTube tube = new SteelTube(insertPoint, 100, 150, 4.0, 0.0);
        
        // 2. 创建多个不同类型的定位点
        AnchorPointManager manager = AnchorPointManager.Instance;
        
        // 插入控制点（底部中心）
        DbPt anchorPos1 = new DbPt(200, 125); // 矩管底部中心
        AnchorPoint insertAnchor = manager.CreateAnchorPoint(
            anchorPos1, AnchorPoint.AnchorType.Insert, 0.0, 8.0);
        
        // 拉伸控制点（右侧中心）
        DbPt anchorPos2 = new DbPt(250, 200); // 矩管右侧中心
        AnchorPoint stretchAnchor = manager.CreateAnchorPoint(
            anchorPos2, AnchorPoint.AnchorType.Control, 0.0, 10.0);
        
        // 对齐参考点（左上角）
        DbPt anchorPos3 = new DbPt(150, 275); // 矩管左上角
        AnchorPoint alignAnchor = manager.CreateAnchorPoint(
            anchorPos3, AnchorPoint.AnchorType.Align, 45.0, 6.0);
        
        // 3. 建立绑定关系
        tube.AddAnchorBinding(insertAnchor, TubeAnchorBinding.BindingType.InsertPoint, 
                             TubeAnchorBinding.RelativePosition.BottomCenter);
        
        tube.AddAnchorBinding(stretchAnchor, TubeAnchorBinding.BindingType.StretchControl, 
                             TubeAnchorBinding.RelativePosition.RightCenter);
        
        tube.AddAnchorBinding(alignAnchor, TubeAnchorBinding.BindingType.AlignReference, 
                             TubeAnchorBinding.RelativePosition.TopLeft);
        
        Console.WriteLine($"已创建 {tube.BoundAnchorCount} 个定位点绑定");
    }

    /// <summary>
    /// 定位点管理示例：展示全局控制和查询功能
    /// </summary>
    public static void ManagementDemo()
    {
        AnchorPointManager manager = AnchorPointManager.Instance;
        
        // 1. 全局控制
        Console.WriteLine("=== 定位点全局控制 ===");
        Console.WriteLine($"当前定位点数量: {manager.Count}");
        Console.WriteLine($"全局可见性: {manager.GlobalVisible}");
        Console.WriteLine($"全局启用状态: {manager.GlobalEnabled}");
        
        // 2. 设置全局状态
        manager.SetAllVisible(true);
        manager.SetAllEnabled(true);
        
        // 3. 按类型控制
        manager.SetTypeEnabled(AnchorPoint.AnchorType.Insert, true);
        manager.SetTypeEnabled(AnchorPoint.AnchorType.Control, false);
        
        // 4. 查询和统计
        var stats = manager.GetBindingStatistics();
        Console.WriteLine("=== 绑定统计 ===");
        foreach (var stat in stats)
        {
            Console.WriteLine($"{stat.Key}: {stat.Value} 个绑定");
        }
        
        // 5. 位置查询
        DbPt searchPos = new DbPt(200, 200);
        AnchorPoint nearest = manager.FindNearestAnchorPoint(searchPos, 50.0);
        if (nearest != null)
        {
            Console.WriteLine($"找到最近定位点: {nearest.AnchorType} at ({nearest.Position.X}, {nearest.Position.Y})");
        }
    }

    /// <summary>
    /// 智能拉伸示例：展示定位点控制的拉伸逻辑
    /// </summary>
    public static void SmartStretchingDemo()
    {
        // 1. 创建带定位点的矩管
        DbPt insertPoint = new DbPt(300, 300);
        SteelTube tube = new SteelTube(insertPoint, 60, 100, 3.0, 0.0);
        tube.EnableAnchorPoints = true;
        tube.EnableSmartStretching = true;
        tube.AnchorPosition = TubeAnchorBinding.RelativePosition.BottomLeft;
        
        // 2. 获取主定位点绑定
        var primaryBinding = tube.GetPrimaryAnchor();
        if (primaryBinding != null)
        {
            // 3. 模拟拖拽操作
            DbPt mousePos1 = new DbPt(400, 350); // 向右上拖拽
            var suggestion1 = tube._anchorBindings[0].GetStretchSuggestion(mousePos1);
            Console.WriteLine($"向右上拖拽建议: 水平={suggestion1.AllowHorizontal}, 垂直={suggestion1.AllowVertical}");
            
            DbPt mousePos2 = new DbPt(250, 280); // 向左下拖拽（靠近定位点）
            var suggestion2 = tube._anchorBindings[0].GetStretchSuggestion(mousePos2);
            Console.WriteLine($"向左下拖拽建议: 水平={suggestion2.AllowHorizontal}, 垂直={suggestion2.AllowVertical}");
            Console.WriteLine($"限制原因: {suggestion2.Reason}");
        }
    }

    /// <summary>
    /// 变换同步示例：展示矩管变换时定位点的同步更新
    /// </summary>
    public static void TransformSyncDemo()
    {
        // 1. 创建矩管和定位点
        DbPt insertPoint = new DbPt(400, 400);
        SteelTube tube = new SteelTube(insertPoint, 80, 120, 3.0, 0.0);
        tube.EnableAnchorPoints = true;
        
        AnchorPoint primaryAnchor = tube.GetPrimaryAnchor();
        if (primaryAnchor != null)
        {
            Console.WriteLine($"初始定位点位置: ({primaryAnchor.Position.X:F1}, {primaryAnchor.Position.Y:F1})");
            
            // 2. 移动矩管
            tube.EleMove(50, 30);
            Console.WriteLine($"移动后定位点位置: ({primaryAnchor.Position.X:F1}, {primaryAnchor.Position.Y:F1})");
            
            // 3. 旋转矩管
            DbPt rotCenter = new DbPt(450, 430);
            tube.EleMove_r(rotCenter, Math.PI / 4); // 旋转45度
            Console.WriteLine($"旋转后定位点位置: ({primaryAnchor.Position.X:F1}, {primaryAnchor.Position.Y:F1})");
            
            // 4. 修改矩管尺寸
            tube.Width = 100;
            tube.Height = 140;
            Console.WriteLine($"尺寸修改后定位点位置: ({primaryAnchor.Position.X:F1}, {primaryAnchor.Position.Y:F1})");
        }
    }

    /// <summary>
    /// 运行所有示例
    /// </summary>
    public static void RunAllDemos()
    {
        Console.WriteLine("=== 矩管定位点系统演示 ===\n");
        
        try
        {
            Console.WriteLine("1. 基本使用示例");
            BasicUsageDemo();
            Console.WriteLine();
            
            Console.WriteLine("2. 高级使用示例");
            AdvancedUsageDemo();
            Console.WriteLine();
            
            Console.WriteLine("3. 定位点管理示例");
            ManagementDemo();
            Console.WriteLine();
            
            Console.WriteLine("4. 智能拉伸示例");
            SmartStretchingDemo();
            Console.WriteLine();
            
            Console.WriteLine("5. 变换同步示例");
            TransformSyncDemo();
            Console.WriteLine();
            
            Console.WriteLine("=== 演示完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"演示过程中发生错误: {ex.Message}");
        }
    }
}

/// <summary>
/// 定位点系统最佳实践指南
/// </summary>
public static class BestPracticesGuide
{
    /// <summary>
    /// 性能优化建议
    /// </summary>
    public static void PerformanceOptimizations()
    {
        Console.WriteLine("=== 性能优化建议 ===");
        Console.WriteLine("1. 只在需要时启用定位点功能（EnableAnchorPoints = true）");
        Console.WriteLine("2. 合理控制定位点数量，避免为单个图元创建过多定位点");
        Console.WriteLine("3. 使用AnchorPointManager的批量操作方法");
        Console.WriteLine("4. 定期清理无效的绑定关系");
        Console.WriteLine("5. 在大量图元操作时，临时禁用自动同步功能");
    }

    /// <summary>
    /// 使用模式建议
    /// </summary>
    public static void UsagePatterns()
    {
        Console.WriteLine("=== 使用模式建议 ===");
        Console.WriteLine("1. 插入控制模式：主要用于控制图元的位置");
        Console.WriteLine("   - 使用 BottomCenter, LeftCenter 等标准位置");
        Console.WriteLine("   - 适合墙体、柱子等建筑构件");
        
        Console.WriteLine("2. 拉伸控制模式：用于动态调整图元尺寸");
        Console.WriteLine("   - 定位点控制拉伸的允许方向");
        Console.WriteLine("   - 启用智能拉伸避免不合理变形");
        
        Console.WriteLine("3. 对齐参考模式：用于图元间的对齐");
        Console.WriteLine("   - 提供吸附和对齐参考");
        Console.WriteLine("   - 适合精确定位需求");
    }

    /// <summary>
    /// 常见问题解决方案
    /// </summary>
    public static void CommonIssues()
    {
        Console.WriteLine("=== 常见问题解决方案 ===");
        Console.WriteLine("1. 定位点位置不正确");
        Console.WriteLine("   - 检查相对位置设置是否正确");
        Console.WriteLine("   - 确认矩管的几何中心计算");
        
        Console.WriteLine("2. 智能拉伸不生效");
        Console.WriteLine("   - 确认 EnableSmartStretching = true");
        Console.WriteLine("   - 检查定位点绑定类型是否为 StretchControl");
        
        Console.WriteLine("3. 变换同步异常");
        Console.WriteLine("   - 检查 EnableAutoSync 设置");
        Console.WriteLine("   - 确认变换操作后调用了同步方法");
        
        Console.WriteLine("4. 内存泄漏");
        Console.WriteLine("   - 及时清理删除图元的绑定关系");
        Console.WriteLine("   - 使用 ValidateBindings 方法检查无效绑定");
    }
}