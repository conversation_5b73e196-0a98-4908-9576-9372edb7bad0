    public class LineToCurtainWallCmd : IExternalCmd
    {
        public string Describe()
        {
            return "线变幕墙";
        }

        public Result Execute(EBDB EbDb)
        {
            if(ArcGFunc.IsExitDraw(EbDb))
            {
                return Result.Cancelled;
            }

            List<ArcSecType> arcSecTypes = new List<ArcSecType>();
            List<OutLineCutType> outLineTypes = new List<OutLineCutType>();
            List<PanelType> panelTypes = new List<PanelType>();
            List<CurtainWallType> curtainWallTypes = new List<CurtainWallType>();

            foreach (var item in EBDB.Instance.GProject.ElementTypes)
            {
                if (item is ArcSecType atype) arcSecTypes.Add(atype);
                else if (item is OutLineCutType otype) outLineTypes.Add(otype);
                else if (item is PanelType ptype) panelTypes.Add(ptype);
                else if (item is CurtainWallType ctype) curtainWallTypes.Add(ctype);
            }

            for (int i = 0; i < DefaultArcSecTypes.SectionLoops.Count; i++)
            {
                bool newType = true;
                foreach (var gtype in arcSecTypes)
                {
                    if (DefaultArcSecTypes.SectionLoops[i].Name == gtype.Name)
                    {
                        /* BaseLoopTypeData.MemberBaseLoopTypeList[i] = gtype; */
                        newType = false;
                        break;
                    }
                }
                if (newType) { EBDB.Instance.GProject.AddEleType(DefaultArcSecTypes.SectionLoops[i]); }
            }

            for (int i = 0; i < OutLineTypeManager.OutLineCutList.Count; i++)
            {
                bool newType = true;
                foreach (var gtype in outLineTypes)
                {
                    if (OutLineTypeManager.OutLineCutList[i].Name == gtype.Name)
                    {
                        //OutLineTypeManager.OutLineCutList[i] = gtype;
                        newType = false;
                        break;
                    }
                }
                if (newType) { EBDB.Instance.GProject.AddEleType(OutLineTypeManager.OutLineCutList[i]); }
            }

            for (int i = 0; i < PanelTypeManager.PanelTypeList.Count; i++)
            {
                bool newType = true;
                foreach (var gtype in panelTypes)
                {
                    if (PanelTypeManager.PanelTypeList[i].Name == gtype.Name)
                    {
                        //PanelTypeManager.PanelTypeList[i] = gtype; 
                        newType = false;
                        break;
                    }
                }
                if (newType) { EBDB.Instance.GProject.AddEleType(PanelTypeManager.PanelTypeList[i]); }
            }

            for (int i = 0; i < CurtainTypeList.CurtainWallTypeList.Count; i++)
            {
                bool newType = true;
                foreach (var gtype in curtainWallTypes)
                {
                    if (CurtainTypeList.CurtainWallTypeList[i].Name == gtype.Name)
                    {
                        //CurtainTypeList.curtainWallTypeList[i] = gtype;
                        newType = false;
                        break;
                    }
                }
                if (newType) { EBDB.Instance.GProject.AddEleType(CurtainTypeList.CurtainWallTypeList[i]); }
            }

            MainDispatcher.Run(() =>
            {
                DrawCurtainWallMainWindow.Instance.IsDXBQ = true;
                EBDB.Instance.InsertPageLeft(DrawCurtainWallMainWindow.Instance);
            });

            // 直线、圆弧或多义线
            var eles = EbDb.UIBoxSelect("选择要生成幕墙的直线、多义线和圆弧：", new LineToWall_LineFilter());
            // 全部转换为直线和圆弧
            if (eles.Count == 0)
            {
                MainDispatcher.Run(() => { EBDB.Instance.ReMovePageLeft(DrawCurtainWallMainWindow.Instance.Title); });
                return Result.Failed;
            }


            //bool isWholeDiv = false;
            //foreach(var ele in eles)
            //{
            //    if (ele is Polyline polyline&& polyline.Lines.Count >0)
            //    {
            //        isWholeDiv = true;
            //        break;
            //    }
            //}

            //if(isWholeDiv)
            //{
            //    MainDispatcher.Run(() =>
            //    {
            //        DrawCurtainWallMainWindow.Instance.IsDXBQ = true;
            //        EBDB.Instance.InsertPageLeft(DrawCurtainWallMainWindow.Instance);
            //    });
            //}
            //else
            //{
            //    MainDispatcher.Run(() =>
            //    {
            //        EBDB.Instance.InsertPageLeft(DrawCurtainWallMainWindow.Instance);
            //    });
            //}
          

            var data = DrawCurtainWallMainWindow.Instance.curtData;

            List<CurtainWall> curWalls = new List<CurtainWall>();
            foreach (var ele in eles)
            {
                if (ele is Line line)
                {
                    if (line.Lines[0].GetLong() > 300)
                    {
                        CurtainWall _curWall = ArcGFunc.CreateCurtainFromLine(line.Lines[0], data, data.VerDivMod, EbDb);
                        if (_curWall != null) { curWalls.Add(_curWall); }
                    }
                }
                else if (ele is Polyline polyline)
                 {
                    DbLine line0 = GMath.LineArcOffset(polyline.Lines[0], true, 200);

                    double offsetDis = 0;
                    switch (data.Position)
                    {
                        case "外侧":
                            offsetDis=data.WallOffset; break;
                        case "中心线":
                            offsetDis = data.WallOffset + data.WallThick / 2; break;
                        case "内侧":
                            offsetDis = data.WallOffset + data.WallThick; break;
                        default: break;
                    }
                    if(!GMath.IsEqual(offsetDis,0))
                    {
                        polyline = ArcGFunc.OffsetPolyLine(polyline, offsetDis, line0.PtMid);
                    }

                    if (polyline == null ) continue;

                    if (polyline.Lines.Count==1)
                    {
                        if (polyline.Lines[0].GetLong() > 300)
                        {
                            CurtainWall _curWall = ArcGFunc.CreateCurtainFromLine(polyline.Lines[0], data, data.VerDivMod, EbDb);
                            if (_curWall != null) { curWalls.Add(_curWall); }
                        }
                    }
                    else
                    {
                        if(data.DivideSet==CurtainDivideSetEnum.Part)
                        {
                            foreach (var li in polyline.Lines)
                            {
                                CurtainWall _curWall = ArcGFunc.CreateCurtainFromLine(li, data, null, EbDb);
                                if (_curWall != null) { curWalls.Add(_curWall); }
                            }
                        }
                        else
                        {
                            List<DbLine> allLines = polyline.Lines.ToList();
                            List<DbLine> newLines = new List<DbLine>();
                            List<DbLine> segLines = new List<DbLine>();
                            double endLen, stLen = 0;
                            List<double> models = new List<double>();
                            if (data.VerDivideStyle == VerDivideStyleEnum.FixedModulus)
                            {
                                models = ArcGFunc.GetDivMod(data.VerDivMod);
                                models = models.Count == 0 ? new List<double>() { 1200 } : models;
                            }
                            else if (data.VerDivideStyle == VerDivideStyleEnum.FixedQuantity)
                            {
                                int divNum = data.VerDivNum <= 0 ? 1 : data.VerDivNum;
                                models.Add(polyline.Length / divNum);
                            }
                            if (models.Count == 0) models.Add(1200);
                            int corModId = 0;

                            for (int i = 0; i < allLines.Count; i++)
                            {
                                var li = allLines[i];
                                if (li.GetLong() < stLen)
                                {
                                    stLen -= li.GetLong();
                                    segLines.Add(li);
                                }

                                else
                                {
                                    DbPt cpt = li.IfArc ? GMath.GetArcCenter(li) : null;
                                    int curModId = stLen > 0 ? (corModId + 1 == models.Count ? 0 : corModId + 1) : corModId;
                                    string corMod = null;
                                    string mainMod = null;

                                    //线段主体划分模数
                                    List<double> prePart = models.GetRange(0, curModId);
                                    List<double> newMod = models.GetRange(curModId, models.Count - curModId);
                                    newMod.AddRange(prePart);
                                    mainMod = string.Join("_", newMod);

                                    //线段前侧转交线段列表的划分模数
                                    if (stLen > 0)
                                    {
                                        prePart = models.GetRange(0, corModId);
                                        newMod = models.GetRange(corModId, models.Count - corModId);
                                        newMod.AddRange(prePart);
                                        corMod = string.Join("_", newMod);
                                    }

                                    //主体线段符合模数的线段长度，同时更新下一个转角的模数编号
                                    double mainLen = 0;
                                    corModId = curModId;
                                    while (mainLen + models[corModId] < li.GetLong() - stLen || GMath.IsEqual(mainLen + models[corModId], li.GetLong() - stLen, 0.01))
                                    {
                                        mainLen += models[corModId];
                                        corModId = corModId + 1 == models.Count ? 0 : corModId + 1;
                                    }
                                    endLen = li.GetLong() - stLen - mainLen;
                                    DbPt newPtSt = li.PtSt;
                                    DbLine newMainLine = null;

                                    if (cpt == null)
                                    {
                                        if (stLen > 0)
                                        {
                                            newPtSt = newPtSt.Move(stLen * li.GetDir());
                                            segLines.Add(new DbLine(li.PtSt, newPtSt));
                                            CurtainWall _curWall = ArcGFunc.CreateCurtainFromLine(NewCorArc(segLines), data, corMod, EbDb);
                                            if (_curWall != null) { curWalls.Add(_curWall); }
                                        }

                                        stLen = models[corModId] - endLen;
                                        segLines.Clear();
                                        if (mainLen > models.Min() - 1)
                                        {
                                            newMainLine = new DbLine(newPtSt, newPtSt.Move(mainLen * li.GetDir()));
                                        }
                                        if (endLen > 0)
                                        {
                                            if (i == allLines.Count - 1)
                                            {
                                                newMainLine = new DbLine(newPtSt, newPtSt.Move((mainLen + endLen) * li.GetDir()));
                                            }
                                            else
                                            {
                                                DbLine reLine = new DbLine(newPtSt.Move(mainLen * li.GetDir()), li.PtEnd);
                                                segLines.Add(reLine);
                                            }
                                        }

                                    }
                                    else
                                    {
                                        double rad = li.GetArcRadius();
                                        bool? dir = GMath.ArcDir(li);

                                        if (stLen > 0)
                                        {
                                            double stAng = dir == true ? stLen / rad : -stLen / rad;
                                            newPtSt = GMath.PtRotate(cpt, li.PtSt, stAng);

                                            DbLine arcLine = GMath.GetArcNew(li.PtSt, newPtSt, cpt, dir ?? false);
                                            segLines.Add(arcLine);
                                            CurtainWall _curWall = ArcGFunc.CreateCurtainFromLine(NewCorArc(segLines), data, corMod, EbDb);
                                            if (_curWall != null) { curWalls.Add(_curWall); }
                                        }

                                        stLen = models[corModId] - endLen;
                                        DbPt newPtEnd = GMath.PtRotate(cpt, newPtSt, (dir == true ? 1 : -1) * mainLen / rad);
                                        segLines.Clear();

                                        if (mainLen > models.Min() - 1)
                                        {
                                            newMainLine = GMath.GetArcNew(newPtSt, newPtEnd, cpt, dir ?? false);
                                        }

                                        if (endLen > 0)
                                        {
                                            if (i == allLines.Count - 1)
                                            {
                                                newMainLine = GMath.GetArcNew(newPtSt, li.PtEnd, cpt, dir ?? false);
                                            }
                                            else
                                            {
                                                DbLine reLine = GMath.GetArcNew(newPtEnd, li.PtEnd, cpt, dir ?? false);
                                                segLines.Add(reLine);
                                            }
                                        }
                                    }
                                    if (newMainLine != null)
                                    {
                                        CurtainWall _curWall = ArcGFunc.CreateCurtainFromLine(newMainLine, data, mainMod, EbDb);
                                        if (_curWall != null) { curWalls.Add(_curWall); }
                                    }
                                }
                            }
                        }
                      
                    }
                }
            }
            DrawCurtainWallMainWindow.Instance.IsDXBQ = false;

            using (Transaction transaction = new Transaction("单线变墙"))
            {
                EbDb.ActivView2D.GView.AddElement(curWalls);
            }

            DbLine NewCorArc(List<DbLine> segLines)
            {
                DbLine refLine = new DbLine(segLines.First().PtSt, segLines.Last().PtEnd);
                refLine = refLine.Rotate(refLine.PtMid, Math.PI / 2);
                GMath.Extend(refLine, 2000, 2000);
                List<DbPt> crossPts = new List<DbPt>();
                foreach (var seg in segLines)
                {
                    List<DbPt> tmpPts = GMath.LineOrArcSegCrossPt(refLine, seg);
                    if (tmpPts.Count > 0)
                    {
                        crossPts.AddRange(tmpPts);
                        break;
                    }
                }

                DbLine corArc = new DbLine(segLines.First().PtSt, segLines.Last().PtEnd, crossPts[0]);
                return corArc;
            }
            MainDispatcher.Run(() => { EBDB.Instance.ReMovePageLeft(DrawCurtainWallMainWindow.Instance.Title); });
            return Result.Succeeded;
        }

        public string Name()
        {
            return "单线变墙";
        }
    }

