    /// <summary>
    /// 铝单板
    /// </summary>
    [Serializable] //序列化标识，所有图元必须有这个标签
    [DbElement("铝单板", MajorType.CurtainWall)] //图元中文名称标签
    public class AluminumPanel : DbElement, ElementToBlockCmd.IConvertibleToBlock
    {
        //图元参数设置原则：
        //1. 明确该参数是否必须有，如果不是必须有，尽量不要在图元中定义
        //2. 在参数必须有的情况下，明确当前的参数方案是否为最优，应尽量确保参数为最优
        //3. 临时使用的参数，严禁设置到图元中
        //4. 显示到属性栏的参数不宜过多，过多的参数将导致属性栏可用性降低
        //5. 当必要的参数过多时，建议用类进行包装，通过另外的对话框进行呈现，而不是直接放到属性栏
        //6. 图元内的所有字段、属性、方法、参数必须添加完整的注释

        //无优化意识，随意添加参数的危害
        //1. 内存占用提升
        //2. 程序性能降低
        //3. 数据易用性降低
        //4. 代码可读性降低

        #region 标识数据
        /// <summary>
        /// 是否闭合
        /// </summary>
        private bool _close = false;
        /// <summary>
        /// 是否闭合
        /// </summary>
        [Category("标识数据"), DisplayName("是否闭合"), Description("铝单板是否闭合"), ReadOnly(false)]
        public bool Closed
        {
            get { return _close; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Closed = a, _close);
                _close = value;
                if (_close)//由非闭合变为闭合
                {

                    int iCt1 = ConPts.Count - 1;

                    //如果控制点列表中最后一个点为普通点
                    if (ConPts[iCt1].PtType == 0)
                    {

                        //倒数第二点是否为弧线中点，若不是连接首尾计算其中点
                        if (ConPts[iCt1 - 1].PtType != 1)
                        {
                            if (ConPts.Count <= 3)
                            {
                                EBDB.Instance.UICmdLine("单段铝单板无法闭合", null, null);
                                return;
                            }

                            DbPt pt = 0.5 * (ConPts[iCt1] + ConPts[0]);
                            pt.PtType = 2; //设置类型为普通中点
                            ConPts.Add(pt); //添加到控制点列表中
                        }
                        else
                        {
                            //如果倒数第二点是弧线中点，
                            DbPt ptSt = ConPts[iCt1];
                            DbPt ptEnd = ConPts[0];
                            DbLine arc = new DbLine(ConPts[iCt1 - 2], ptSt, ConPts[iCt1 - 1]);
                            DbLine lineT = GMath.GetArcTangentLine(arc, false);
                            DbPt dirLast = lineT.PtSt - lineT.PtEnd;
                            DbPt dirT = new DbPt(-dirLast.Y, dirLast.X); //垂直方向
                            DbPt pt1 = ptSt + dirT;
                            DbLine line1 = new DbLine(ptSt, pt1);
                            DbLine line2 = new DbLine(ptSt, ptEnd);
                            DbLine line3 = GMath.GetVerLine(line2, line2.PtMid); //垂直平分线
                            DbPt ptC = GMath.LineCrossPt(line1, line3);
                            if (ptC == null)
                            {
                                DbPt pt = 0.5 * (ConPts[iCt1] + ConPts[0]);
                                pt.PtType = 2; //这是为普通中点
                                ConPts.Add(pt); //添加到控制点列表中
                            }
                            else
                            {
                                double rr = GMath.Distance(ptC, ptSt);
                                DbPt dirNew = line3.PtEnd - line3.PtSt; //获取垂直线的方向
                                dirNew.Normalize(); //归一化方向向量
                                double temp = dirLast * dirNew;
                                if (temp < 0) { dirNew = -dirNew; }//计算夹角，大于90°方向相反
                                DbPt ptMid = ptC + rr * dirNew; //计算中点
                                ptMid.PtType = 1; //弧形中点
                                ConPts.Add(ptMid);
                            }
                        }
                    }

                }
                else//闭合变为非闭合
                {
                    int iCt1 = ConPts.Count - 1;
                    if (ConPts[iCt1].PtType == 1 || ConPts[iCt1].PtType == 2)
                    {
                        //如果最后一个点是弧线中点或普通点，则删除
                        ConPts.RemoveAt(iCt1);
                    }
                }

                if (AutoActivate) { ActAndCal2D(); }
            }
        }

        /// <summary>
        /// 方向控制
        /// </summary>
        private bool _direction = true;
        /// <summary>
        /// 方向控制
        /// </summary>
        [Category("标识数据"), DisplayName("内外朝向"), Description("铝单板朝向"), ReadOnly(false)]
        public bool Direction
        {
            get { return _direction; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Direction = a, _direction);

                _direction = value;

                //对于二维图元，如果参数改变导致形体变化，调用：ActAndCal2D()
                //对于二维图元，如果参数改变不会导致图元形体变化，不需要调用
                //对于三维图元，如果参数改变导致形体变化，调用：ActCutCal2D3D()
                //对于三维图元，如果参数改变不会导致图元形体变化，不需要调用
                ActCutCal2D3D();
            }
        }
        #endregion

        #region 几何参数
        /// <summary>
        /// 厚度
        /// </summary>
        private double _thickness;
        /// <summary>
        /// 厚度
        /// </summary>
        [Category("几何参数"), DisplayName("厚度"), Description("铝单板厚度"), ReadOnly(false)]
        public double Thickness
        {
            get { return _thickness; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => Thickness = a, _thickness);

                _thickness = value;

                //对于二维图元，如果参数改变导致形体变化，调用：ActAndCal2D()
                //对于二维图元，如果参数改变不会导致图元形体变化，不需要调用
                //对于三维图元，如果参数改变导致形体变化，调用：ActCutCal2D3D()
                //对于三维图元，如果参数改变不会导致图元形体变化，不需要调用
                //ActAndCal2D();
                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 折边距离
        /// </summary>
        private double _edgeDistance;
        /// <summary>
        /// 折边距离
        /// </summary>
        [Category("几何参数"), DisplayName("折边距离"), Description("铝单板折边距离"), ReadOnly(false)]
        public double EdgeDistance
        {
            get { return _edgeDistance; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => EdgeDistance = a, _edgeDistance);

                _edgeDistance = value;

                //对于二维图元，如果参数改变导致形体变化，调用：ActAndCal2D()
                //对于二维图元，如果参数改变不会导致图元形体变化，不需要调用
                //对于三维图元，如果参数改变导致形体变化，调用：ActCutCal2D3D()
                //对于三维图元，如果参数改变不会导致图元形体变化，不需要调用
                //ActAndCal2D();
                ActCutCal2D3D();
            }
        }

        ///<summary>
        ///总长度
        ///</summary>
        private double _length = 0;
        ///<summary>
        ///总长度
        ///</summary>
        [Category("几何参数"), DisplayName("总长度"), Description("铝单板的总长度。"), ReadOnly(true)]
        public double Length { get { return _length; } }

        /// <summary>
        /// 铝单板宽度是否随出图比例自适应
        /// </summary>
        private bool _autoScale = false;
        /// <summary>
        /// 铝单板宽度是否随出图比例自适应
        /// </summary>
        [Category("几何参数"), DisplayName("出图比例自适应"), Description("铝单板宽度是否随出图比例自适应。"), ReadOnly(false)]
        public bool AutoScale { get { return _autoScale; } set { _autoScale = value; if (AutoActivate) { ActCutCal2D3D(); } } }

        /// <summary>
        /// 起始端折边
        /// </summary>
        private bool _leftEdge = false;
        /// <summary>
        /// 起始端折边
        /// </summary>
        [Category("几何参数"), DisplayName("起始端折边"), Description("铝单板起始端折边"), ReadOnly(false)]
        public bool LeftEdge
        {
            get { return _leftEdge; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => LeftEdge = a, _leftEdge);
                _leftEdge = value;

                ActCutCal2D3D();
            }
        }

        /// <summary>
        /// 终止端折边
        /// </summary>
        private bool _rightEdge = false;
        /// <summary>
        /// 终止端折边
        /// </summary>
        [Category("几何参数"), DisplayName("终止端折边"), Description("铝单板终止端折边"), ReadOnly(false)]
        public bool RightEdge
        {
            get { return _rightEdge; }
            set
            {
                //将本次参数改变推送到事务栈，用于实现回退，没有这行在参数被修改时无法回退
                TransManager.Instance().Push(a => RightEdge = a, _rightEdge);
                _rightEdge = value;

                if (!_close)
                {
                    if (_rightEdge)
                    {

                    }
                }
                else
                {
                    //如果是闭合铝单板，左侧折边无效
                    _rightEdge = false;
                }

                ActCutCal2D3D();
            }
        }

        #endregion

        #region 材料及表面处理
        /// <summary>
        /// 材质
        /// </summary>
        private DbMaterial _material = new DbMaterial("铝 - 铝单板");
        /// <summary>
        /// 材质
        /// </summary>
        [Category("材料"), DisplayName("构件材料"), Description("当前构件材料"), ReadOnly(false)]
        [Editor(typeof(Btn_MatSelect), typeof(Btn_MatSelect))]
        public DbMaterial Material
        {
            get => _material;
            set
            {
                if (_material == value) return;
                TransManager.Instance().Push(a => Material = a, _material);
                _material = value;
                if (AutoActivate)
                {
                    ActCutCal2D3D();
                }
                //EBDB.Instance.GProject.GetAllEle3DSet();
            }

        }

        /// <summary>
        /// 表面处理方式
        /// </summary>
        //private DbSurfaceTreatment _surfaceTreatment = new DbSurfaceTreatment();
        /// <summary>
        /// 表面处理方式
        /// </summary>
        //[Category("表面处理方式"), DisplayName("表面处理方式"), Description("表面处理方式"), ReadOnly(false)]
        //[Editor(typeof(EnumEditor), typeof(EnumEditor))]
        //public DbSurfaceTreatment SurfaceType
        //{
        //    get => _surfaceTreatment;
        //    set
        //    {
        //        if (_surfaceTreatment == value) return;
        //        TransManager.Instance().Push(a => SurfaceType = a, _surfaceTreatment);
        //        _surfaceTreatment = value;
        //        if (AutoActivate) { ActCutCal2D3D(); }

        //    }
        //}

        private SurfaceTreatmentType _surfaceTreatment = new SurfaceTreatmentType();

        [Category("表面处理方式"), DisplayName("表面处理方式"), Description("表面处理方式"), ReadOnly(false)]
        [Editor(typeof(EnumEditor), typeof(EnumEditor))]

        public SurfaceTreatmentType SurfaceTreatment
        {
            get => _surfaceTreatment;
            set
            {
                if (_surfaceTreatment == value) return;
                TransManager.Instance().Push(a => SurfaceTreatment = a, _surfaceTreatment);
                _surfaceTreatment = value;
                if (AutoActivate) { ActCutCal2D3D(); }

            }
        }

        #endregion

        #region 其他属性
        /// <summary>
        /// 铝单板基线
        /// </summary>
        [Browsable(false)]
        public List<DbLine> LocationCurve
        {
            get
            {
                List<DbPt> pts = GetOutPts();
                int iCt = pts.Count;        // 点的总数
                int iCt1 = iCt - 1;         // 最后一个点的索引
                List<DbLine> Linebase = new List<DbLine>();

                if (_close)
                {
                    // 处理闭合铝单板
                    for (int i = 0; i < iCt; i++)
                    {
                        int iP = i != iCt1 ? i + 1 : 0;

                        DbPt pt = pts[i];
                        DbPt ptP = pts[iP];

                        if (ptP.PtType == 0)
                        {
                            //基准线
                            var lineBase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                            Linebase.Add(lineBase);
                        }
                        else//构造函数里，保证了i + 2一定有
                        {
                            int iQ = iP != iCt1 ? i + 2 : 0;

                            //基准线
                            DbLine lineBase = new DbLine(pt.EleCopy(), pts[iQ].EleCopy(), ptP.EleCopy());
                            Linebase.Add(lineBase);
                            i++;
                        }
                    }
                }
                else // 处理非闭合多义线
                {
                    for (int i = 0; i < iCt1; i++)
                    {
                        DbPt pt = pts[i];        // 当前点
                        DbPt ptP = pts[i + 1];   // 下一个点

                        // 如果下一个点是普通点（非控制点）
                        if (ptP.PtType == 0)
                        {
                            // 创建直线段并添加到集合
                            DbLine linebase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                            Linebase.Add(linebase);
                        }
                        else //（用于创建曲线）
                        {
                            // 创建曲线段并添加到集合
                            DbLine linebase = new DbLine(pt.EleCopy(), pts[i + 2].EleCopy(), ptP.EleCopy());
                            Linebase.Add(linebase);
                            i++;
                        }
                    }
                }
                return Linebase;
            }
        }

        #endregion

        /// <summary>
        /// 无参构造函数，每个图元必须保留无参构造函数
        /// </summary>
        public AluminumPanel()
        {
            //每个图元必须在图元中定义是否为三维
            _if3D = false;
            LayerSet("金属面材"); // 设置图层名称
            _close = false; //是否闭合
            _direction = true; //方向控制
            _thickness = 3; //铝单板厚度
            _edgeDistance = 20; //折边距离
            _leftEdge = false; //起始端是否折边
            _rightEdge = false; //终止端是否折边
        }


        #region 备份代码
        /// <summary>
        /// 铝单板构造函数(不闭合）
        /// </summary>
        /// <param name="pts">铝单板顶点</param>
        /// <param name="thickness">铝单板厚度</param>
        /// <param name="edgeDistance">铝单板折边距离</param>
        /// <param name="direction">铝单板朝向</param>
        /// <param name="layer">图层ID</param>
        //public AluminumPanel(List<DbPt> pts, double thickness, double edgeDistance, bool direction, int layerId)
        //{
        //    LayerSet("金属面材"); // 设置图层名称

        //    _if3D = false;
        //    _thickness = thickness;
        //    _edgeDistance = edgeDistance;
        //    _direction = direction;
        //    _layerId = layerId;
        //    layerId = PreLayerManage.GetLayerId("金属面材"); // 设置图层ID

        //    if (pts == null || pts.Count < 2)
        //    {
        //        ConPts.Clear();
        //        return;
        //    }

        //    int iCt = pts.Count; // 输入顶点的总数
        //    int iCt1 = iCt - 1;  // 输入顶点列表的最后一个索引

        //    // 遍历输入的顶点列表以构建ConPts (控制点列表)
        //    // 循环少一次，因为我们总是处理点i和点i+1之间的线段
        //    for (int i = 0; i < iCt1; i++)
        //    {
        //        int iP = i + 1; // 当前处理线段的终点索引 (在输入pts列表中的索引)
        //        DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
        //        DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)

        //        // 判断当前线段是直线还是弧线，或者是铝单板的最后一段
        //        // ptP.PtType == 0 表示ptP是一个普通顶点，通常意味着 pt -> ptP 是一条直线
        //        // iP == iCt1 表示ptP是输入列表中的最后一个点，因此 pt -> ptP 是铝单板的最后一段
        //        if (ptP.PtType == 0 || iP == iCt1)
        //        {
        //            // 处理直线段或铝单板的最后一段
        //            DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
        //            ptT.PtType = 0;          // 标记为普通顶点 (类型0)
        //            ConPts.Add(ptT);         // 添加到ConPts列表

        //            // 计算并添加当前线段的普通中点
        //            DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
        //            ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //            ConPts.Add(ptM); // 添加到ConPts列表

        //            // 如果当前处理的是输入列表中的最后一段 (即ptP是最后一个输入点)
        //            if (iP == iCt1)
        //            {
        //                DbPt ptPT = ptP.EleCopy(); // 复制这最后一个输入点
        //                ptPT.PtType = 0;           // 标记为普通顶点
        //                ConPts.Add(ptPT);          // 添加到ConPts，作为铝单板的实际终点
        //            }
        //        }
        //        else // 处理弧线段 (当ptP.PtType != 0 且不是最后一段时，通常表示ptP是弧中点)
        //        {
        //            // 弧线段由三点定义：pts[i] (起点), pts[iP] (弧上一点/中点), pts[iP+1] (终点)
        //            DbPt ptT = pt.EleCopy(); // 复制弧线的起点
        //            ptT.PtType = 0;          // 标记为普通顶点
        //            ConPts.Add(ptT);         // 添加到ConPts列表

        //            int iQ = iP + 1;      // 弧线终点在输入pts列表中的索引
        //            DbPt ptQ = pts[iQ];  // 获取弧线的终点

        //            //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
        //            DbPt ptM = GMath.GetArcMid(ptT, ptQ, ptP);

        //            if (ptM != null) // 如果成功计算出精确的弧中点
        //            {
        //                ptM.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
        //                ConPts.Add(ptM); // 添加到ConPts列表
        //            }
        //            else // 如果计算弧中点失败 (例如三点共线)
        //            {
        //                // 退而求其次，使用起点和终点的中点作为普通中点
        //                ptM = 0.5 * (pt + ptQ);
        //                ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //                ConPts.Add(ptM);
        //            }

        //            // 如果这条弧线是输入列表中的最后一段（即ptQ是最后一个输入顶点）
        //            if (iQ == iCt1)
        //            {
        //                DbPt ptQT = ptQ.EleCopy(); // 复制弧线的终点
        //                ptQT.PtType = 0;           // 标记为普通顶点
        //                ConPts.Add(ptQT);          // 添加到ConPts，作为铝单板的实际终点
        //            }

        //            i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
        //        }
        //    }

        //}

        /// <summary>
        /// 铝单板构造函数(可闭合）
        /// </summary>
        /// <param name="pts">铝单板顶点</param>
        /// <param name="thickness">铝单板厚度</param>
        /// <param name="edgeDistance">铝单板折边距离</param>
        /// <param name="direction">铝单板朝向</param>
        /// <param name="closed">闭合</param>
        /// <param name="layer">图层ID</param>
        //public AluminumPanel(List<DbPt> pts, double thickness, double edgeDistance, bool direction, bool closed, int layerId)
        //{
        //    LayerSet("金属面材"); // 设置图层名称

        //    _if3D = false;
        //    _thickness = thickness;
        //    _edgeDistance = edgeDistance;
        //    _direction = direction;
        //    _layerId = layerId;
        //    _close = closed; // 设置闭合状态
        //    layerId = PreLayerManage.GetLayerId("金属面材"); // 设置图层ID

        //    //if (pts == null || pts.Count < 2)
        //    //{
        //    //    ConPts.Clear();
        //    //    return;
        //    //}
        //    ConPts.Clear();
        //    int iCt = pts.Count; // 输入顶点的总数
        //    int iCt1 = iCt - 1;  // 输入顶点列表的最后一个索引

        //    // 遍历输入的顶点列表以构建ConPts (控制点列表)
        //    // 循环少一次，因为我们总是处理点i和点i+1之间的线段

        //    if (_close)
        //    {
        //        for (int i = 0; i < iCt; i++)
        //        {
        //            int iP = i != iCt1 ? i + 1 : 0; // 当前处理线段的终点索引 (在输入pts列表中的索引)，如果是最后一个点则回到第一个点
        //            DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
        //            DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)
        //            if (iP == 0) // 如果是闭合铝单板的最后一段
        //            {
        //                DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
        //                ptT.PtType = 0;          // 标记为普通顶点 (类型0)
        //                ConPts.Add(ptT);         // 添加到ConPts列表
        //                // 计算并添加当前线段的普通中点
        //                DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
        //                ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //                ConPts.Add(ptM); // 添加到ConPts列表

        //            }
        //            else if (ptP.PtType == 1)
        //            {
        //                // 如果当前线段是弧线段
        //                DbPt ptT = pt.EleCopy(); // 复制弧线的起点
        //                ptT.PtType = 0;          // 标记为普通顶点
        //                ConPts.Add(ptT);         // 添加到ConPts列表

        //                int iQ = iP != iCt1 ? i + 2 : 0;
        //                DbPt ptQ = pts[iQ];  // 获取弧线的终点

        //                //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
        //                DbPt ptPT = GMath.GetArcMid(ptT, ptQ, ptP);
        //                if (ptPT != null) // 如果成功计算出精确的弧中点
        //                {
        //                    ptPT.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
        //                    ConPts.Add(ptPT); // 添加到ConPts列表
        //                }
        //                else // 如果计算弧中点失败 (例如三点共线)
        //                {
        //                    // 退而求其次，使用起点和终点的中点作为普通中点
        //                    DbPt ptM = 0.5 * (pt + ptQ);
        //                    ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //                    ConPts.Add(ptM);
        //                }

        //                i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
        //            }
        //            else // 如果当前线段是直线段
        //            {
        //                DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
        //                ptT.PtType = 0;          // 标记为普通顶点 (类型0)
        //                ConPts.Add(ptT);         // 添加到ConPts列表
        //                // 计算并添加当前线段的普通中点
        //                DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
        //                ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //                ConPts.Add(ptM); // 添加到ConPts列表
        //            }
        //        }
        //    }
        //    else
        //    {
        //        for (int i = 0; i < iCt1; i++)
        //        {
        //            int iP = i + 1; // 当前处理线段的终点索引 (在输入pts列表中的索引)
        //            DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
        //            DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)

        //            // 判断当前线段是直线还是弧线，或者是铝单板的最后一段
        //            // ptP.PtType == 0 表示ptP是一个普通顶点，通常意味着 pt -> ptP 是一条直线
        //            // iP == iCt1 表示ptP是输入列表中的最后一个点，因此 pt -> ptP 是铝单板的最后一段
        //            if (ptP.PtType == 0 || iP == iCt1)
        //            {
        //                // 处理直线段或铝单板的最后一段
        //                DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
        //                ptT.PtType = 0;          // 标记为普通顶点 (类型0)
        //                ConPts.Add(ptT);         // 添加到ConPts列表

        //                // 计算并添加当前线段的普通中点
        //                DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
        //                ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //                ConPts.Add(ptM); // 添加到ConPts列表

        //                // 如果当前处理的是输入列表中的最后一段 (即ptP是最后一个输入点)
        //                if (iP == iCt1)
        //                {
        //                    DbPt ptPT = ptP.EleCopy(); // 复制这最后一个输入点
        //                    ptPT.PtType = 0;           // 标记为普通顶点
        //                    ConPts.Add(ptPT);          // 添加到ConPts，作为铝单板的实际终点
        //                }
        //            }
        //            else // 处理弧线段 (当ptP.PtType != 0 且不是最后一段时，通常表示ptP是弧中点)
        //            {
        //                // 弧线段由三点定义：pts[i] (起点), pts[iP] (弧上一点/中点), pts[iP+1] (终点)
        //                DbPt ptT = pt.EleCopy(); // 复制弧线的起点
        //                ptT.PtType = 0;          // 标记为普通顶点
        //                ConPts.Add(ptT);         // 添加到ConPts列表

        //                int iQ = iP + 1;      // 弧线终点在输入pts列表中的索引
        //                DbPt ptQ = pts[iQ];  // 获取弧线的终点

        //                //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
        //                DbPt ptM = GMath.GetArcMid(ptT, ptQ, ptP);

        //                if (ptM != null) // 如果成功计算出精确的弧中点
        //                {
        //                    ptM.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
        //                    ConPts.Add(ptM); // 添加到ConPts列表
        //                }
        //                else // 如果计算弧中点失败 (例如三点共线)
        //                {
        //                    // 退而求其次，使用起点和终点的中点作为普通中点
        //                    ptM = 0.5 * (pt + ptQ);
        //                    ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
        //                    ConPts.Add(ptM);
        //                }

        //                // 如果这条弧线是输入列表中的最后一段（即ptQ是最后一个输入顶点）
        //                if (iQ == iCt1)
        //                {
        //                    DbPt ptQT = ptQ.EleCopy(); // 复制弧线的终点
        //                    ptQT.PtType = 0;           // 标记为普通顶点
        //                    ConPts.Add(ptQT);          // 添加到ConPts，作为铝单板的实际终点
        //                }

        //                i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
        //            }
        //        }

        //    }

        //}

        /// <summary>
        /// 铝单板构造函数(可闭合, 根据线段创建铝单板）
        /// </summary>
        /// <param name="pts">铝单板顶点</param>
        /// <param name="thickness">铝单板厚度</param>
        /// <param name="edgeDistance">铝单板折边距离</param>
        /// <param name="direction">铝单板朝向</param>
        /// <param name="closed">闭合</param>
        /// <param name="layer">图层ID</param>
        //public AluminumPanel(List<DbLine> lines, double thickness, double edgeDistance, bool direction, bool closed, int layerId)
        //{
        //    LayerSet("金属面材"); // 设置图层名称

        //    _if3D = false;
        //    _thickness = thickness;
        //    _edgeDistance = edgeDistance;
        //    _direction = direction;
        //    _layerId = layerId;
        //    _close = closed; // 设置闭合状态

        //    ConPts.Clear(); // 清空控制点列表
        //    var ls = lines.Select(l => l.EleCopy()).ToList();
        //    ls = GFunc.SortLines(lines);
        //    if (ls == null)
        //    {
        //        EBDB.Instance.UICmdLine("铝单板基线不连续！", "", "");
        //        return;
        //    }

        //    foreach (var l in ls)
        //    {
        //        if (!l.IfArc)
        //        {
        //            ConPts.Add(l.PtSt.EleCopy());
        //        }
        //        else
        //        {
        //            ConPts.Add(l.PtSt.EleCopy());
        //            ConPts.Add(l.PtMid.EleCopy());
        //            ConPts.Last().PtType = 1;
        //        }
        //    }
        //    ConPts.Add(ls.Last().PtEnd.EleCopy());

        //}

        #endregion

        /// <summary>
        /// 铝单板构造函数(不闭合,仅传递点集、绘制方向、其余参数外部传递）
        /// </summary>
        /// <param name="pts">铝单板顶点</param>
        /// <param name="direction">铝单板朝向</param>
        public AluminumPanel(List<DbPt> pts, bool direction)
        {
            LayerSet("金属面材"); // 设置图层名称

            _if3D = false;
            _direction = direction;
            _thickness = 3; //铝单板厚度
            _edgeDistance = 20; //折边距离
            _leftEdge = false; //起始端是否折边
            _rightEdge = false; //终止端是否折边

            if (pts == null || pts.Count < 2)
            {
                ConPts.Clear();
                return;
            }

            int iCt = pts.Count; // 输入顶点的总数
            int iCt1 = iCt - 1;  // 输入顶点列表的最后一个索引

            // 遍历输入的顶点列表以构建ConPts (控制点列表)
            // 循环少一次，因为我们总是处理点i和点i+1之间的线段
            for (int i = 0; i < iCt1; i++)
            {
                int iP = i + 1; // 当前处理线段的终点索引 (在输入pts列表中的索引)
                DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
                DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)

                // 判断当前线段是直线还是弧线，或者是铝单板的最后一段
                // ptP.PtType == 0 表示ptP是一个普通顶点，通常意味着 pt -> ptP 是一条直线
                // iP == iCt1 表示ptP是输入列表中的最后一个点，因此 pt -> ptP 是铝单板的最后一段
                if (ptP.PtType == 0 || iP == iCt1)
                {
                    // 处理直线段或铝单板的最后一段
                    DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                    ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                    ConPts.Add(ptT);         // 添加到ConPts列表

                    // 计算并添加当前线段的普通中点
                    DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                    ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                    ConPts.Add(ptM); // 添加到ConPts列表

                    // 如果当前处理的是输入列表中的最后一段 (即ptP是最后一个输入点)
                    if (iP == iCt1)
                    {
                        DbPt ptPT = ptP.EleCopy(); // 复制这最后一个输入点
                        ptPT.PtType = 0;           // 标记为普通顶点
                        ConPts.Add(ptPT);          // 添加到ConPts，作为铝单板的实际终点
                    }
                }
                else // 处理弧线段 (当ptP.PtType != 0 且不是最后一段时，通常表示ptP是弧中点)
                {
                    // 弧线段由三点定义：pts[i] (起点), pts[iP] (弧上一点/中点), pts[iP+1] (终点)
                    DbPt ptT = pt.EleCopy(); // 复制弧线的起点
                    ptT.PtType = 0;          // 标记为普通顶点
                    ConPts.Add(ptT);         // 添加到ConPts列表

                    int iQ = iP + 1;      // 弧线终点在输入pts列表中的索引
                    DbPt ptQ = pts[iQ];  // 获取弧线的终点

                    //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
                    DbPt ptM = GMath.GetArcMid(ptT, ptQ, ptP);

                    if (ptM != null) // 如果成功计算出精确的弧中点
                    {
                        ptM.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
                        ConPts.Add(ptM); // 添加到ConPts列表
                    }
                    else // 如果计算弧中点失败 (例如三点共线)
                    {
                        // 退而求其次，使用起点和终点的中点作为普通中点
                        ptM = 0.5 * (pt + ptQ);
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM);
                    }

                    // 如果这条弧线是输入列表中的最后一段（即ptQ是最后一个输入顶点）
                    if (iQ == iCt1)
                    {
                        DbPt ptQT = ptQ.EleCopy(); // 复制弧线的终点
                        ptQT.PtType = 0;           // 标记为普通顶点
                        ConPts.Add(ptQT);          // 添加到ConPts，作为铝单板的实际终点
                    }

                    i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
                }
            }

        }

        /// <summary>
        /// 铝单板构造函数(可闭合,仅传递点集、绘制方向、是否闭合、其余参数外部传递）
        /// </summary>
        /// <param name="pts">铝单板顶点</param>
        /// <param name="direction">铝单板朝向</param>
        /// <param name="closed">闭合</param>
        public AluminumPanel(List<DbPt> pts, bool direction, bool closed)
        {
            LayerSet("金属面材"); // 设置图层名称

            _if3D = false;
            _direction = direction;
            _close = closed; // 设置闭合状态
            _thickness = 3; //铝单板厚度
            _edgeDistance = 20; //折边距离
            _leftEdge = false; //起始端是否折边
            _rightEdge = false; //终止端是否折边


            if (pts == null || pts.Count < 2)
            {
                ConPts.Clear();
                return;
            }

            ConPts.Clear();
            int iCt = pts.Count; // 输入顶点的总数
            int iCt1 = iCt - 1;  // 输入顶点列表的最后一个索引

            // 遍历输入的顶点列表以构建ConPts (控制点列表)
            // 循环少一次，因为我们总是处理点i和点i+1之间的线段

            if (_close)
            {
                for (int i = 0; i < iCt; i++)
                {
                    int iP = i != iCt1 ? i + 1 : 0; // 当前处理线段的终点索引 (在输入pts列表中的索引)，如果是最后一个点则回到第一个点
                    DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
                    DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)
                    if (iP == 0) // 如果是闭合铝单板的最后一段
                    {
                        DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                        ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                        ConPts.Add(ptT);         // 添加到ConPts列表
                        // 计算并添加当前线段的普通中点
                        DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM); // 添加到ConPts列表

                    }
                    else if (ptP.PtType == 1)
                    {
                        // 如果当前线段是弧线段
                        DbPt ptT = pt.EleCopy(); // 复制弧线的起点
                        ptT.PtType = 0;          // 标记为普通顶点
                        ConPts.Add(ptT);         // 添加到ConPts列表

                        int iQ = iP != iCt1 ? i + 2 : 0;
                        DbPt ptQ = pts[iQ];  // 获取弧线的终点

                        //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
                        DbPt ptPT = GMath.GetArcMid(ptT, ptQ, ptP);
                        if (ptPT != null) // 如果成功计算出精确的弧中点
                        {
                            ptPT.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
                            ConPts.Add(ptPT); // 添加到ConPts列表
                        }
                        else // 如果计算弧中点失败 (例如三点共线)
                        {
                            // 退而求其次，使用起点和终点的中点作为普通中点
                            DbPt ptM = 0.5 * (pt + ptQ);
                            ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                            ConPts.Add(ptM);
                        }

                        i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
                    }
                    else // 如果当前线段是直线段
                    {
                        DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                        ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                        ConPts.Add(ptT);         // 添加到ConPts列表
                        // 计算并添加当前线段的普通中点
                        DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM); // 添加到ConPts列表
                    }
                }
            }
            else
            {
                for (int i = 0; i < iCt1; i++)
                {
                    int iP = i + 1; // 当前处理线段的终点索引 (在输入pts列表中的索引)
                    DbPt pt = pts[i];   // 当前线段的起点 (来自输入pts列表)
                    DbPt ptP = pts[iP]; // 当前线段的终点 (来自输入pts列表)

                    // 判断当前线段是直线还是弧线，或者是铝单板的最后一段
                    // ptP.PtType == 0 表示ptP是一个普通顶点，通常意味着 pt -> ptP 是一条直线
                    // iP == iCt1 表示ptP是输入列表中的最后一个点，因此 pt -> ptP 是铝单板的最后一段
                    if (ptP.PtType == 0 || iP == iCt1)
                    {
                        // 处理直线段或铝单板的最后一段
                        DbPt ptT = pt.EleCopy(); // 复制当前线段的起点
                        ptT.PtType = 0;          // 标记为普通顶点 (类型0)
                        ConPts.Add(ptT);         // 添加到ConPts列表

                        // 计算并添加当前线段的普通中点
                        DbPt ptM = 0.5 * (pt + ptP); // 计算中点坐标
                        ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                        ConPts.Add(ptM); // 添加到ConPts列表

                        // 如果当前处理的是输入列表中的最后一段 (即ptP是最后一个输入点)
                        if (iP == iCt1)
                        {
                            DbPt ptPT = ptP.EleCopy(); // 复制这最后一个输入点
                            ptPT.PtType = 0;           // 标记为普通顶点
                            ConPts.Add(ptPT);          // 添加到ConPts，作为铝单板的实际终点
                        }
                    }
                    else // 处理弧线段 (当ptP.PtType != 0 且不是最后一段时，通常表示ptP是弧中点)
                    {
                        // 弧线段由三点定义：pts[i] (起点), pts[iP] (弧上一点/中点), pts[iP+1] (终点)
                        DbPt ptT = pt.EleCopy(); // 复制弧线的起点
                        ptT.PtType = 0;          // 标记为普通顶点
                        ConPts.Add(ptT);         // 添加到ConPts列表

                        int iQ = iP + 1;      // 弧线终点在输入pts列表中的索引
                        DbPt ptQ = pts[iQ];  // 获取弧线的终点

                        //DbPt ptM = TDMath.GetTDArcMid(ptT, ptQ, ptP);
                        DbPt ptM = GMath.GetArcMid(ptT, ptQ, ptP);

                        if (ptM != null) // 如果成功计算出精确的弧中点
                        {
                            ptM.PtType = 1; // 标记为铝单板上的圆弧中点 (类型1)
                            ConPts.Add(ptM); // 添加到ConPts列表
                        }
                        else // 如果计算弧中点失败 (例如三点共线)
                        {
                            // 退而求其次，使用起点和终点的中点作为普通中点
                            ptM = 0.5 * (pt + ptQ);
                            ptM.PtType = 2; // 标记为铝单板的普通中点 (类型2)
                            ConPts.Add(ptM);
                        }

                        // 如果这条弧线是输入列表中的最后一段（即ptQ是最后一个输入顶点）
                        if (iQ == iCt1)
                        {
                            DbPt ptQT = ptQ.EleCopy(); // 复制弧线的终点
                            ptQT.PtType = 0;           // 标记为普通顶点
                            ConPts.Add(ptQT);          // 添加到ConPts，作为铝单板的实际终点
                        }

                        i++; // 因为弧线段一次处理了输入点中的两个"段"（i到iP, iP到iQ），所以循环变量i需要额外加1
                    }
                }

            }

        }

        /// <summary>
        /// 铝单板构造函数(可闭合, 线段创建铝单板，仅传递点集、绘制方向、是否闭合）
        /// </summary>
        /// <param name="lines">铝单板线段</param>
        /// <param name="direction">铝单板朝向</param>
        /// <param name="closed">闭合</param>
        public AluminumPanel(List<DbLine> lines, bool direction, bool closed)
        {
            LayerSet("金属面材"); // 设置图层名称

            _if3D = false;
            _direction = direction;
            _close = closed; // 设置闭合状态
            _thickness = 3; //铝单板厚度
            _edgeDistance = 20; //折边距离
            _leftEdge = false; //起始端是否折边
            _rightEdge = false; //终止端是否折边

            ConPts.Clear(); // 清空控制点列表
            var ls = lines.Select(l => l.EleCopy()).ToList();
            ls = GFunc.SortLines(lines);
            if (ls == null)
            {
                EBDB.Instance.UICmdLine("铝单板基线不连续！", "", "");
                return;
            }

            foreach (var l in ls)
            {
                if (!l.IfArc)
                {
                    ConPts.Add(l.PtSt.EleCopy());
                }
                else
                {
                    ConPts.Add(l.PtSt.EleCopy());
                    ConPts.Add(l.PtMid.EleCopy());
                    ConPts.Last().PtType = 1;
                }
            }
            ConPts.Add(ls.Last().PtEnd.EleCopy());

        }

        ///<summary>
        ///获得铝单板基准线的外轮廓点，去除ConPts列表中所有直线段中点，保留弯弧中点
        /// </summary>
        ///<param name="ifRetureArcMid">是否返回弧线中点</param>
        ///<returns></returns>
        public List<DbPt> GetOutPts(bool ifRetureArcMid = true)
        {
            List<DbPt> pts = new List<DbPt>();
            if (ifRetureArcMid)
            {
                foreach (DbPt pt in ConPts) { if (pt.PtType != 2) { pts.Add(pt); } }
            }
            else
            {
                foreach (DbPt pt in ConPts) { if (pt.PtType == 0) { pts.Add(pt); } }
            }
            return pts;
        }

        /// <summary>
        /// 保存数据
        /// </summary>
        /// <param name="binaryWriter">写入流</param>
        public override void DataSave(BinaryWriter binaryWriter)
        {
            //图元类标识，必要
            binaryWriter.Write(this.GetType().ToString());

            //版本号，必要，每次版本的升级必须注明时间和变更内容
            //0版：2024.10.30 新增
            binaryWriter.Write(0);

            //图元共有参数，必要，该方法对DbElement类的公共数据进行了保存
            PubSave(binaryWriter);

            //分界线之上的代码为每个图元的固定格式，不能修改
            //-------分界线---------
            //分界线之下的代码为每个图元特有参数，根据图元的需要确定参数是否保存
            //在进行数据保存时，应确保保存的参数尽可能精简

            //图元特有参数,根据每个图元情况进行保存
            binaryWriter.Write(_thickness);
            binaryWriter.Write(_edgeDistance);
            binaryWriter.Write(_direction);
            binaryWriter.Write(_close);
            binaryWriter.Write(_length);
            binaryWriter.Write(_autoScale);
            binaryWriter.Write(_leftEdge);
            binaryWriter.Write(_rightEdge);
            binaryWriter.Write((Int16)_surfaceTreatment);
            _material.DataSave(binaryWriter);
        }

        /// <summary>
        /// 装载数据
        /// </summary>
        /// <param name="binaryReader">读取流</param>
        public override void DataLoad(BinaryReader binaryReader)
        {
            //图元数据的读顺序必须与图元的写顺序完全一致
            //图元的类标识平台已经读取了，在图元中不需要再读
            //在图元的读写方法中不能使用try/catch对错误进行捕捉，
            //读写中不能容忍出现未知的异常

            //每个图元的版本号是相互独立的，并且与软件整体的版本号无关
            //版本号的设定是为了解决软件发布后，在类数据变化时，高版本能兼容低版本的问题
            //在写图元时，必须完全理解版本号的运作逻辑
            //对版本号的理解如果存在偏差，可能在软件发布后导致用户数据丢失等严重后果！

            //读取版本号，必要
            int VerNum = binaryReader.ReadInt32();

            //每个版本号的读写都必须保留
            if (VerNum == 0)
            {
                #region
                //图元共有参数，必要，该方法对DbElement类的公共数据进行了读取
                PubLoad(binaryReader);

                //图元特有参数
                _thickness = binaryReader.ReadDouble();
                _edgeDistance = binaryReader.ReadDouble();
                _direction = binaryReader.ReadBoolean();
                _close = binaryReader.ReadBoolean();
                _length = binaryReader.ReadDouble();
                _autoScale = binaryReader.ReadBoolean();
                _leftEdge = binaryReader.ReadBoolean();
                _rightEdge = binaryReader.ReadBoolean();
                _surfaceTreatment = (SurfaceTreatmentType)binaryReader.ReadInt16();
                _material.DataLoad(binaryReader);
                #endregion
            }
        }

        /// <summary>
        /// 返回图元的一个备份(深度复制)，每个图元必须重写该方法
        /// </summary>
        /// <param name="changeUid">深度复制时是否改变 UniqueId true：改变 false：不改变</param>
        /// <returns>新的图元</returns>
        public override DbElement EleCopy(bool changeUid = false)
        {
            //在C#中，两个类如果直接赋值，传递的是引用，两个类本身均指向同一地址，
            //修改任意一个，两个都会被修改
            //图元深度复制的原理是将图元的每个参数深入到值类型进行赋值，
            //确保深度复制后的图元与原图元可以独立修改而不互相影响

            AluminumPanel ele = new AluminumPanel();

            //图元共有参数，必要
            PubCopy(ele);

            //图元特有参数
            ele._thickness = _thickness;
            ele._edgeDistance = _edgeDistance;
            ele._direction = _direction;
            ele._close = _close;
            ele._length = _length;
            ele._autoScale = _autoScale;
            ele._leftEdge = _leftEdge;
            ele._rightEdge = _rightEdge;
            ele._surfaceTreatment = _surfaceTreatment;
            ele._material = _material;

            if (changeUid) { ele.UniqueId = Guid.NewGuid().ToString(); } //改变Uid
            return ele;
        }

        /// <summary>
        /// 激活，图元激活的本质是通过"控制点"+"参数"计算出所有构成图元的点、线、文字、填充等基本要素
        /// </summary>
        public override void Activate()
        {
            //在图元被移动、拉伸等变化时，图元的动画是通过不断的调用激活函数形成的，
            //因此在每次激活时，需要清空之前计算存储的数据
            //图元的显示要素存储的列表包括：Lines、Texts、Circles、Hatchs四个结合，可以根据图元需要进行选用

            // 设置缩放比例，如果启用了自动缩放，则使用视图比例
            double tempSc = 1;
            if (_autoScale) { tempSc = ViewSc / 100.0; }

            Lines.Clear();

            // 获取铝单板的所有点（不包括直线段中点）
            List<DbPt> pts = GetOutPts();

            int iCt = pts.Count;        // 点的总数
            int iCt1 = iCt - 1;         // 最后一个点的索引

            // 初始化铝单板长度
            _length = 0;

            if (_close)
            {
                // 计算偏移点集
                List<DbPt> thicknessPts = GMath.GetOffsetArcPts(pts, _thickness, _direction);
                List<DbPt> edgeDistancePts = GMath.GetOffsetArcPts(pts, _edgeDistance, _direction);

                // 检查偏移是否成功（偏移失败时返回null）
                if (thicknessPts == null) return;
                if (edgeDistancePts == null) return;

                // 处理闭合铝单板
                for (int i = 0; i < iCt; i++)
                {
                    int iP = i != iCt1 ? i + 1 : 0;

                    DbPt pt = pts[i];
                    DbPt ptP = pts[iP];
                    DbPt thicknessPt = thicknessPts[i];
                    DbPt thicknessPtP = thicknessPts[iP];
                    DbPt edgeDistancePt = edgeDistancePts[i];
                    DbPt edgeDistancePtP = edgeDistancePts[iP];

                    if (ptP.PtType == 0)
                    {
                        //基准线
                        DbLine lineBase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        //厚度线
                        DbLine lineThickness = new DbLine(thicknessPt.EleCopy(), thicknessPtP.EleCopy());
                        Lines.Add(lineThickness);

                        //折边线
                        DbLine lineEdgeDistance = new DbLine(edgeDistancePt.EleCopy(), edgeDistancePtP.EleCopy());
                        Lines.Add(lineEdgeDistance);
                    }
                    else//构造函数里，保证了i + 2一定有
                    {
                        int iQ = iP != iCt1 ? i + 2 : 0;

                        //基准线
                        DbLine lineBase = new DbLine(pt.EleCopy(), pts[iQ].EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        //厚度线
                        DbLine lineThickness = new DbLine(thicknessPt.EleCopy(), thicknessPts[iQ].EleCopy(), thicknessPtP.EleCopy());
                        Lines.Add(lineThickness);

                        //折边线
                        DbLine lineEdgeDistance = new DbLine(edgeDistancePt.EleCopy(), edgeDistancePts[iQ].EleCopy(), edgeDistancePtP.EleCopy());
                        Lines.Add(lineEdgeDistance);

                        i++;
                    }
                }
            }
            else // 处理非闭合铝单板
            {
                // 计算偏移点集
                List<DbPt> thicknessPts = GMath.GetUnclosedOffsetArcPts(pts, _thickness, _direction);
                List<DbPt> edgeDistancePts = GMath.GetUnclosedOffsetArcPts(pts, _edgeDistance, _direction);

                //检查偏移是否成功（偏移失败时返回null）
                if (thicknessPts == null) return;
                if (edgeDistancePts == null) return;

                // === 计算端头折边的交点（统一处理）
                DbPt leftThicknessPt = null;
                DbPt leftEdgePt = null;
                DbPt rightThicknessPt = null;
                DbPt rightEdgePt = null;

                // 计算左端折边交点
                if (_leftEdge)
                {
                    DbLine leftEdgeLine = GMath.LineOffset(
                        new DbLine(thicknessPts[0].EleCopy(), edgeDistancePts[0].EleCopy()),
                        !_direction, _thickness);

                    // 判断第一段是直线还是弧线
                    if (pts[1].PtType == 0) // 第一段是直线
                    {
                        leftThicknessPt = GMath.GetTwoLineCrossPt(leftEdgeLine,
                            new DbLine(thicknessPts[0].EleCopy(), thicknessPts[1].EleCopy()));
                        leftEdgePt = GMath.GetTwoLineCrossPt(leftEdgeLine,
                            new DbLine(edgeDistancePts[0].EleCopy(), edgeDistancePts[1].EleCopy()));
                    }
                    else // 第一段是弧线
                    {
                        // 创建厚度线弧和折边线弧
                        DbLine thicknessArc = new DbLine(thicknessPts[0].EleCopy(), thicknessPts[2].EleCopy(), thicknessPts[1].EleCopy());
                        DbLine edgeArc = new DbLine(edgeDistancePts[0].EleCopy(), edgeDistancePts[2].EleCopy(), edgeDistancePts[1].EleCopy());

                        leftThicknessPt = GMath.GetTwoLineCrossPt(leftEdgeLine, thicknessArc);
                        leftEdgePt = GMath.GetTwoLineCrossPt(leftEdgeLine, edgeArc);
                    }
                }

                // 计算右端折边交点
                if (_rightEdge)
                {
                    DbLine rightEdgeLine = GMath.LineOffset(
                        new DbLine(thicknessPts[iCt1].EleCopy(), edgeDistancePts[iCt1].EleCopy()),
                        _direction, _thickness);

                    // 判断最后一段是直线还是弧线
                    if (pts[iCt1 - 1].PtType != 1) // 最后一段是直线（倒数第二个点不是弧中点）
                    {
                        rightThicknessPt = GMath.GetTwoLineCrossPt(rightEdgeLine,
                            new DbLine(thicknessPts[iCt1 - 1].EleCopy(), thicknessPts[iCt1].EleCopy()));
                        rightEdgePt = GMath.GetTwoLineCrossPt(rightEdgeLine,
                            new DbLine(edgeDistancePts[iCt1 - 1].EleCopy(), edgeDistancePts[iCt1].EleCopy()));
                    }
                    else // 最后一段是弧线
                    {
                        // 创建厚度线弧和折边线弧
                        DbLine thicknessArc = new DbLine(thicknessPts[iCt1 - 2].EleCopy(), thicknessPts[iCt1].EleCopy(), thicknessPts[iCt1 - 1].EleCopy());
                        DbLine edgeArc = new DbLine(edgeDistancePts[iCt1 - 2].EleCopy(), edgeDistancePts[iCt1].EleCopy(), edgeDistancePts[iCt1 - 1].EleCopy());

                        rightThicknessPt = GMath.GetTwoLineCrossPt(rightEdgeLine, thicknessArc);
                        rightEdgePt = GMath.GetTwoLineCrossPt(rightEdgeLine, edgeArc);
                    }
                }

                // === 主循环：处理每个线段
                for (int i = 0; i < iCt1; i++)
                {
                    DbPt pt = pts[i];        // 当前点
                    DbPt ptP = pts[i + 1];   // 下一个点
                    DbPt thicknessPt = thicknessPts[i];
                    DbPt thicknessPtP = thicknessPts[i + 1];
                    DbPt edgeDistancePt = edgeDistancePts[i];
                    DbPt edgeDistancePtP = edgeDistancePts[i + 1];

                    // 绘制基准线
                    if (ptP.PtType == 0) // 直线段
                    {
                        DbLine lineBase = new DbLine(pt.EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        DbLine lineEdgeDistance = new DbLine(edgeDistancePt.EleCopy(), edgeDistancePtP.EleCopy());
                        Lines.Add(lineEdgeDistance);

                    }
                    else // 弧线段
                    {
                        DbLine lineBase = new DbLine(pt.EleCopy(), pts[i + 2].EleCopy(), ptP.EleCopy());
                        Lines.Add(lineBase);
                        _length += _if3D ? lineBase.GetLong3D() : lineBase.GetLong();

                        DbLine lineEdgeDistance = new DbLine(edgeDistancePts[i].EleCopy(), edgeDistancePts[i + 2].EleCopy(), edgeDistancePts[i + 1].EleCopy());
                        Lines.Add(lineEdgeDistance);

                        i++; // 跳过弧线中点
                    }
                }

                // === 绘制厚度线（统一处理）
                DrawThicknessLines(thicknessPts, leftThicknessPt, rightThicknessPt, pts);

                //铝单板起始端封口线
                Lines.Add(new DbLine(pts[0].EleCopy(), thicknessPts[0].EleCopy()));
                Lines.Add(new DbLine(thicknessPts[0].EleCopy(), edgeDistancePts[0].EleCopy()));
                //铝单板终止端封口线
                Lines.Add(new DbLine(pts[iCt1].EleCopy(), thicknessPts[iCt1].EleCopy()));
                Lines.Add(new DbLine(thicknessPts[iCt1].EleCopy(), edgeDistancePts[iCt1].EleCopy()));

                // === 绘制端部线条
                // 左端处理
                if (_leftEdge && leftThicknessPt != null && leftEdgePt != null)
                {
                    // 绘制端部折边线
                    Lines.Add(new DbLine(leftThicknessPt.EleCopy(), leftEdgePt.EleCopy()));
                }

                // 右端处理
                if (_rightEdge && rightThicknessPt != null && rightEdgePt != null)
                {
                    Lines.Add(new DbLine(rightThicknessPt.EleCopy(), rightEdgePt.EleCopy()));
                }
            }

            foreach (DbLine line in Lines) { line.SetStatus(1, 1, 1); }
            foreach (DbLine line in Lines) { line.LayerId = PreLayerManage.GetLayerId("金属面材"); }

            LayerChange(layerManage.GetLayer(_layerId));

            if (_styleIndex >= 0) { foreach (DbLine line in Lines) { line.StyleIndex = _styleIndex; } }
            if (_colorIndex >= 0)
            {
                foreach (DbLine line in Lines) { line.ColorIndex = _colorIndex; }
            }

            if (_widthIndex >= 0) { foreach (DbLine line in Lines) { line.WidthIndex = _widthIndex; } }

            EleArea = new DbEleArea(this);
            CalSolid2D();
        }

        /// <summary>
        /// 绘制厚度线的辅助方法
        /// </summary>
        /// <param name="thicknessPts">厚度偏移点集</param>
        /// <param name="leftThicknessPt">左端折边与厚度线的交点</param>
        /// <param name="rightThicknessPt">右端折边与厚度线的交点</param>
        /// <param name="pts">原始点集</param>
        private void DrawThicknessLines(List<DbPt> thicknessPts, DbPt leftThicknessPt, DbPt rightThicknessPt, List<DbPt> pts)
        {
            int iCt = pts.Count;
            int iCt1 = iCt - 1;

            // 确定厚度线的起始和结束点
            DbPt startPt = _leftEdge && leftThicknessPt != null ? leftThicknessPt : thicknessPts[0];
            DbPt endPt = _rightEdge && rightThicknessPt != null ? rightThicknessPt : thicknessPts[iCt1];

            // 如果只有两个点（一条直线）
            if (iCt == 2)
            {
                Lines.Add(new DbLine(startPt.EleCopy(), endPt.EleCopy()));
                return;
            }

            // 处理多段线的厚度线
            for (int i = 0; i < iCt1; i++)
            {
                DbPt currentStart, currentEnd;

                // 确定当前段的起点
                if (i == 0 && _leftEdge && leftThicknessPt != null)
                {
                    currentStart = leftThicknessPt;
                }
                else
                {
                    currentStart = thicknessPts[i];
                }

                // 确定当前段的终点
                if (pts[i + 1].PtType == 0) // 直线段
                {
                    if (i == iCt1 - 1 && _rightEdge && rightThicknessPt != null)
                    {
                        currentEnd = rightThicknessPt;
                    }
                    else
                    {
                        currentEnd = thicknessPts[i + 1];
                    }

                    Lines.Add(new DbLine(currentStart.EleCopy(), currentEnd.EleCopy()));
                }
                else // 弧线段
                {
                    if (i + 2 < iCt)
                    {
                        if (i + 2 == iCt1 && _rightEdge && rightThicknessPt != null)
                        {
                            currentEnd = rightThicknessPt;
                        }
                        else
                        {
                            currentEnd = thicknessPts[i + 2];
                        }

                        // 创建厚度线弧 - 正确的方法
                        int midIndex = i + 1;

                        // 先用厚度线偏移点创建弧线
                        DbLine tempThicknessArc = new DbLine(thicknessPts[i].EleCopy(), thicknessPts[i + 2].EleCopy(), thicknessPts[midIndex].EleCopy());

                        // 从弧线中获取正确的中点
                        DbPt correctMidPt = GMath.GetArcMid(currentStart, currentEnd, tempThicknessArc.PtMid);

                        if (correctMidPt != null)
                        {
                            // 用起点、终点和正确的中点创建厚度线弧
                            DbLine thicknessArc = new DbLine(currentStart.EleCopy(), currentEnd.EleCopy(), correctMidPt.EleCopy());
                            Lines.Add(thicknessArc);
                        }
                        else
                        {
                            // 如果无法计算中点，退化为直线
                            Lines.Add(new DbLine(currentStart.EleCopy(), currentEnd.EleCopy()));
                        }
                        i++; // 跳过弧线中点
                    }
                }
            }
        }

        /// <summary>
        /// 返回图元被单独选中时显示的提示内容
        /// </summary>
        /// <param name="str1">提示文字1</param>
        /// <param name="str2">提示文字2</param>
        public override void EleTips(out string str1, out string str2)
        {
            str1 = _thickness.ToString() + "mm厚" + _surfaceTreatment.GetDescription() + "铝单板";
            str2 = "折边距离 = " + _edgeDistance.ToString();
        }

        ///<summary>
        ///检查圆弧中点是否正确
        /// </summary>
        /// <returns></returns>
        private bool IfHaveWrongArcMid(List<DbPt> pts)
        {
            int iCt = pts.Count; //控制点总数
            int iCt1 = iCt - 1; //控制点列表的最后一个索引
            int iCt2 = iCt - 2; //控制点列表的倒数第二个索引

            if (_close)
            {
                //遍历所有顶点（每次递增2）
                for (int i = 0; i < iCt1; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    DbPt ptM = pts[iM]; //获取中点
                    if (ptM.PtType != 1) { continue; }
                    int ip = i != iCt2 ? i + 2 : 0; //终点（下一个点索引）
                    DbPt pt = pts[i]; //起点
                    DbPt ptP = pts[ip]; //终点
                    DbPt ptC = GMath.GetArcCenter(pt, ptP, ptM); //计算圆心
                    if (ptC == null) { return true; } //如果无法计算圆心，则返回true表示有错误
                }
            }
            else
            {
                //遍历所有顶点（每次递增2）
                for (int i = 0; i < iCt2; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    DbPt ptM = pts[iM]; //获取中点
                    if (ptM.PtType != 1) { continue; }
                    int ip = i + 2; //终点（下一个点索引）
                    DbPt pt = pts[i]; //起点
                    DbPt ptP = pts[ip]; //终点
                    DbPt ptC = GMath.GetArcCenter(pt, ptP, ptM); //计算圆心
                    if (ptC == null) { return true; } //如果无法计算圆心，则返回true表示有错误
                }
            }
            return false;
        }

        ///<summary>
        ///刷新图元在拉伸后的状态
        /// </summary>
        ///<param name="X">X方向的拉伸距离</param> 
        ///<param name="Y">X方向的拉伸距离</param> 
        public override void EleMove_s(double X, double Y)
        {
            //复制一份控制点
            List<DbPt> pts = GFunc.CopyPts(ConPts);

            double X2 = X * 0.5; //X方向拉伸的半径
            double Y2 = Y * 0.5; //Y方向拉伸的半径

            //标记是否有点被处理过、是否存在无法处理的情况
            bool ifHave = false;  //是否被拉伸处理过
            int iCt = pts.Count;  //控制点总数
            int iCt1 = iCt - 1;   //控制点列表的最后一个索引  
            int iCt2 = iCt - 2;   //控制点列表的倒数第二个索引
            bool ifError = false; //是否存在无法处理的情况(如弧中点无法重新计算）

            //处理闭合的铝单板
            if (_close)
            {
                //遍历所有顶点（跳过中点，每次递增2）
                for (int i = 0; i < iCt1; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    int iP = i != iCt2 ? i + 2 : 0; //终点（下一个点索引）
                    DbPt pt = pts[i];
                    DbPt ptP = pts[iP];
                    DbPt ptM = pts[iM]; //中点

                    //情况1：起点和终点都被选中，整段一起移动
                    if (pt.Status == 1 && ptP.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动
                        ptP.MoveSelf(X, Y); //终点移动
                        ifHave = true; //标记为被处理过
                    }
                    //情况2：只有起点被选中
                    else if (pt.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动

                        //如果是圆弧中点，需要重新计算中点位置以保持圆弧的形状
                        if (ptM.PtType == 1)
                        {
                            //根据新起点和原中点重新计算圆弧中点位置
                            DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            //更新中点坐标
                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z; //更新Z坐标
                        }
                        else //普通中点
                        {
                            ptM.MoveSelf(X2, Y2);
                        }
                        ifHave = true; //标记为被处理过
                    }
                    //情况3：只有终点被选中
                    else if (ptP.Status == 1)
                    {
                        //圆弧中点需要特殊处理
                        if (ptM.PtType == 1)
                        {
                            //获取移动后的终点
                            DbPt ptPT;
                            if (iP == 0) { ptPT = ptP.EleCopy(); } //如果首尾相连，则终点为首点复制一点
                            else { ptPT = ptP.Move(X, Y); }

                            //根据原起点和新终点重新计算圆弧中点
                            DbPt ptTemp = GMath.GetArcMid(pt, ptPT, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            //更新中点坐标
                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z;
                        }
                        else //普通中点
                        {
                            ptM.MoveSelf(X2, Y2);
                        }
                        ifHave = true; //标记为被处理过
                    }
                }

            }
            //处理非闭合铝单板
            else
            {
                for (int i = 0; i < iCt2; i = i + 2)
                {
                    int iM = i + 1; //中点索引
                    int iP = i + 2; //终点（下一个点索引）
                    DbPt pt = pts[i];
                    DbPt ptP = pts[iP];
                    DbPt ptM = pts[iM]; //中点

                    //情况1：起点和终点都被选中，整段一起移动
                    if (pt.Status == 1 && ptP.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动
                        ptP.MoveSelf(X, Y); //终点移动
                        ifHave = true; //标记为被处理过
                    }
                    //情况2：只有起点被选中
                    else if (pt.Status == 1)
                    {
                        pt.MoveSelf(X, Y); //起点移动

                        //如果是圆弧中点，需要重新计算中点位置以保持圆弧的形状
                        if (ptM.PtType == 1)
                        {
                            DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z;
                        }
                        else //普通中点
                        {
                            ptM.MoveSelf(X2, Y2);
                        }
                        ifHave = true; //标记为被处理过
                    }
                    //情况3：只有终点被选中
                    else if (ptP.Status == 1)
                    {
                        if (ptM.PtType == 1)
                        {
                            DbPt ptPT = ptP.Move(X, Y); //如果是圆弧中点，复制终点
                            DbPt ptTemp = GMath.GetArcMid(pt, ptPT, ptM);
                            if (ptTemp == null) { ifError = true; break; }//如果无法计算圆弧中点，则标记为错误

                            ptM.X = ptTemp.X;
                            ptM.Y = ptTemp.Y;
                            ptM.Z = ptTemp.Z;
                        }
                        else
                        {
                            ptM.MoveSelf(X2, Y2); //普通中点
                        }
                        //如果是最后一个点，也需要移动
                        if (iP == iCt1) { ptP.MoveSelf(X, Y); } //终点移动
                        ifHave = true;
                    }
                }
            }

            //无错误时，处理选中中点的情况
            if (!ifError && !ifHave)
            {
                //处理闭合
                if (_close)
                {
                    //遍历所有顶点，查找被选中的中点
                    for (int i = 0; i < iCt1; i = i + 2)
                    {
                        int iM = i + 1;
                        int iP = i != iCt2 ? i + 2 : 0; //终点（下一个点索引）
                        DbPt pt = pts[i];
                        DbPt ptP = pts[iP];
                        DbPt ptM = pts[iM]; //中点

                        //如果中点被选中
                        if (ptM.Status == 1)
                        {
                            //情况1：普通中点被选中
                            if (ptM.PtType == 2)
                            {
                                //移动整个线段的三个点
                                pt.MoveSelf(X, Y); //起点移动
                                ptP.MoveSelf(X, Y); //终点移动
                                ptM.MoveSelf(X, Y);

                                //处理前一个中点
                                int iL = i != 0 ? i - 1 : iCt1; //前一个点索引
                                DbPt ptL = pts[iL]; //前一个点
                                ptL.MoveSelf(X2, Y2); //前一个中点移动

                                //如果前一个中点是圆弧中点，需要重新计算位置
                                if (ptL.PtType == 1)
                                {
                                    //根据前一个起点和新的当前起点重新计算圆弧中点位置
                                    DbPt ptTemp = GMath.GetArcMid(pts[iL - 1], pt, ptM);
                                    if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误

                                    //更新前一个中点坐标
                                    ptL.X = ptTemp.X;
                                    ptL.Y = ptTemp.Y;
                                    ptL.Z = ptTemp.Z;
                                }

                                //处理后一个中点
                                DbPt ptR = pts[iP + 1]; //后一个点
                                ptR.MoveSelf(X2, Y2); //后一个中点移动

                                //如果后一个中点是圆弧中点，需要重新计算位置
                                if (ptR.PtType == 1)
                                {
                                    int iQ = iP != iCt2 ? iP + 2 : 0; //后一个点的终点索引

                                    //根据新的当前终点和后一个终点重新计算圆弧中点位置
                                    DbPt ptTemp = GMath.GetArcMid(ptP, pts[iQ], ptR);
                                    if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误

                                    //更新后一个中点坐标
                                    ptR.X = ptTemp.X;
                                    ptR.Y = ptTemp.Y;
                                    ptR.Z = ptTemp.Z;
                                }
                            }
                            //情况2：弧线中点被选中
                            else
                            {
                                ptM.MoveSelf(X, Y); //移动圆弧中点

                                //根据原始起点和终点重新计算圆弧中点位置
                                DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                                if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误

                                ptM.X = ptTemp.X;
                                ptM.Y = ptTemp.Y;
                                ptM.Z = ptTemp.Z;
                            }//圆弧中点只移动自己
                            break;
                        }
                    }
                }
                //处理非闭合
                else
                {
                    for (int i = 0; i < iCt2; i = i + 2)
                    {
                        int iM = i + 1; //中点索引
                        int iP = i + 2; //终点（下一个点索引）
                        DbPt pt = pts[i];
                        DbPt ptP = pts[iP];
                        DbPt ptM = pts[iM];

                        //如果中点被选中
                        if (ptM.Status == 1)
                        {
                            //情况1：普通中点
                            if (ptM.PtType == 2)
                            {
                                //移动整个线段的三个点
                                pt.MoveSelf(X, Y); //起点移动
                                ptP.MoveSelf(X, Y); //终点移动
                                ptM.MoveSelf(X, Y); //中点移动

                                //处理前一个中点(如果存在)
                                if (i >= 2)
                                {
                                    DbPt ptT = pts[i - 1]; //前一个中点
                                    ptT.MoveSelf(X2, Y2); //前一个中点移动

                                    //如果前一个中点是圆弧中点，需要重新计算位置
                                    if (ptT.PtType == 1)
                                    {
                                        //根据前一个起点和新的当前起点重新计算圆弧中点位置
                                        DbPt ptTemp = GMath.GetArcMid(pts[i - 2], pt, ptM);
                                        if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误
                                        //更新前一个中点坐标
                                        ptT.X = ptTemp.X;
                                        ptT.Y = ptTemp.Y;
                                        ptT.Z = ptTemp.Z;
                                    }
                                }

                                //处理后一个中点（如果存在）
                                if (i + 3 < iCt1)
                                {
                                    DbPt ptT = pts[i + 3];
                                    ptT.MoveSelf(X2, Y2); //后一个中点移动

                                    //如果后一个中点是圆弧中点，需要重新计算位置
                                    if (ptT.PtType == 1)
                                    {
                                        //根据新的当前终点和后一个终点重新计算圆弧中点位置
                                        DbPt ptTemp = GMath.GetArcMid(ptP, pts[i + 4], ptT);
                                        if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误
                                        //更新后一个中点坐标
                                        ptT.X = ptTemp.X;
                                        ptT.Y = ptTemp.Y;
                                        ptT.Z = ptTemp.Z;
                                    }
                                }

                            }
                            //情况2： 弧线中点
                            else
                            {
                                ptM.MoveSelf(X, Y); //移动圆弧中点
                                //根据原始起点和终点重新计算圆弧中点位置
                                DbPt ptTemp = GMath.GetArcMid(pt, ptP, ptM);
                                if (ptTemp == null) { ifError = true; break; } //如果无法计算圆弧中点，则标记为错误
                                ptM.X = ptTemp.X;
                                ptM.Y = ptTemp.Y;
                                ptM.Z = ptTemp.Z;
                            }//圆弧中点只移动自己
                            break; //找到被选中的中点后，退出循环
                        }
                    }
                }
            }

            //如果没有错误且所有圆弧中点都能正确计算，则应用拉伸结果
            if (!ifError && !IfHaveWrongArcMid(pts))
            {
                ConPts = pts; //更新控制点列表
                Activate(); //重新激活图元以更新显示
            }
        }

        ///<summary>
        ///图元被选中时执行的附加操作（方向切换）
        ///</summary>
        public override void EleBeSel()
        {
            //如果选中铝单板个数大于1，则不添加翻转按钮
            bool moreThanOne = EBDB.Instance.EleUserSels.Where(item => item is AluminumPanel).Skip(1).Any();
            if (!moreThanOne)
            {

                Hatchs.Clear(); //清空铝单板的填充列表
                DbLine line = new DbLine(ConPts[0], ConPts[1]); //获取铝单板的基准线
                DbPt vec = line.ToVector(); //获取铝单板的方向向量
                vec.Normalize(); //归一化方向向量
                DbControlHatch dbControlHatch = new DbControlHatch();
                double ang = GMath.LineAzimuth(ConPts[0], ConPts[1]);
                ang = ang * 180 / Math.PI + 90; //计算铝单板的方向角度
                //内外翻转
                if (_direction)
                {
                    DbPt norvec = GMath.CalNormDirPt(vec, false); //计算铝单板的法向量
                    DbPt pos = ConPts[1].EleCopy().Move((_edgeDistance + 50) * norvec); //计算铝单板的翻转位置
                    DbControlHatch controlHatch = new DbControlHatch(EleControlType.DArrow, pos, 0.5, ang); //创建铝单板的填充控制器
                    this.Hatchs.Add(controlHatch); //将控制器添加到铝单板的填充列表中

                }
                else
                {
                    DbPt norvec = GMath.CalNormDirPt(vec, false); //计算铝单板的法向量
                    DbPt pos = ConPts[1].EleCopy().Move(-1 * (_edgeDistance + 50) * norvec); //计算铝单板的翻转位置
                    DbControlHatch controlHatch = new DbControlHatch(EleControlType.DArrow, pos, 0.5, ang); //创建铝单板的填充控制器
                    this.Hatchs.Add(controlHatch); //将控制器添加到铝单板的填充列表中
                }
            }
            CalSolid2D(); //重新计算铝单板的2D实体
        }

        ///<summary>    
        ///图元被取消时添加的附加操作
        ///</summary>
        public override void EleBeUnSel()
        {
            //图元在取消选中后将翻转按钮移除
            for (int i = this.Hatchs.Count - 1; i >= 0; i--)
            {
                if (this.Hatchs[i] is DbControlHatch)
                {
                    this.Hatchs.RemoveAt(i);
                }
            }
            Hatchs.Clear(); //清空铝单板的填充列表
            CalSolid2D();
        }

        ///<summary>
        ///用户添加的控件被点击时执行的操作(铝单板切换方向)
        ///</summary>
        /// <param name="rIndex">DbControlHatch在图元Hatchs列表中的倒数序号(rIndex=0代表最后一个)，通过该序号确定是哪个按钮被点击</param>
        public override void EleControlClick(int rIndex)
        {
            //执行图元自定义按钮被按下时的操作逻辑，通过rIndex序号确定是哪个按钮被点击
            if (rIndex == 0)
            {
                if (_direction)
                {
                    _direction = false; //切换方向光
                }
                else
                {
                    _direction = true; //切换方向光
                }
                //var pt0 = ConPts[1].EleCopy();
                //var pt1 = ConPts[0].EleCopy();
                ////ReSetConPts(new List<DbPt> { pt0, pt1, ConPts[2] }, true, true);
                //SetConPts(new List<DbPt> { pt0, pt1, ConPts[2] }, true, true);
                //GFunc.CutElementAndOthers(_view, this);
                ActAndCal2D();
                EleBeSel();

            }
        }

        ///<summary>
        ///刷新铝单板移动后的数据
        ///</summary>
        ///<param name="X">X方向的移动距离</param>
        ///<param name="Y">Y方向的移动距离</param>
        public override void EleMove(double X, double Y)
        {
            //铝单板的移动是通过控制点的移动来实现的
            //如果铝单板被选中，则移动所有控制点
            if (ConPts.Count > 0)
            {
                foreach (DbPt pt in ConPts)
                {
                    pt.MoveSelf(X, Y); //移动控制点
                }
                ActAndCal2D(); //激活并计算2D实体
            }
        }

        /// <summary>
        /// 图元图层或颜色属性被用户修改时调用
        /// </summary>
        // 颜色变更处理：根据颜色索引设置墙体所有线条的颜色
        //public override void ColorChange()
        //{
        //    //如果图元颜色为特定颜色（非随图层）
        //    if (_colorIndex >= 0)
        //    {
        //        int num = Lines.Count; //获取线条数量
        //        for (int i = 0; i < num; i++) 
        //        { 
        //            if (Lines[i].Status0 == 1) 
        //            { 
        //                Lines[i].ColorIndex = _colorIndex; 
        //            } 
        //        }
        //    }
        //    else if (_colorIndex == -1)
        //    {
        //        foreach (DbLine line in Lines)
        //        {
        //            line.ColorIndex = -1; //设置线条颜色为随图层变化
        //        }
        //    }
        //    CalSolid2D();
        //}

        /// <summary>
        /// 图元图层或线型属性被用户修改时调用
        /// </summary>
        // 颜色变更处理：根据颜色索引设置墙体所有线条的颜色
        public override void StyleChange()
        {
            CalSolid2D();
        }

        #region 常规几何修改
        /// <summary>
        /// 基于线的图元延伸到其他图元的线、圆时的行为：点选模式
        /// 将铝单板的基线延伸到与其他图元相交，支持延伸到线条和圆形
        /// </summary>
        /// <param name="eles">目标图元集合，铝单板将延伸到这些图元</param>
        /// <param name="pt">点击点，用于确定延伸方向和目标</param>
        /// <returns>延伸成功返回true，失败返回false</returns>
        public override bool EleExtendWithElesByPt(List<DbElement> eles, DbPt pt)
        {
            if (_close) //闭合铝单板不支持延伸
            {
                EBDB.Instance.UICmdLine("闭合铝单板不支持延伸!", null, null);
                return false;
            }
            // 获取铝单板的基线用于延伸
            List<DbLine> lineToExtend = this.LocationCurve;
            if (lineToExtend == null || lineToExtend.Count == 0) return false;

            // 根据点击点pt确定要延伸的线段(最近的线段)，返回该线段及其在整体中的索引
            var nearestLine = GFunc.GetNearLine(lineToExtend, pt, out int index);
            if (nearestLine == null || nearestLine.GetLong() < 1.0) return false;

            if (!nearestLine.IfArc)
            {
                #region 直线段处理逻辑
                GuiDB.Vector vec;  // 延伸方向向量
                DbPt ptStart;  // 起点

                //确定延伸方向和起始点
                if (lineToExtend.Count == 1)//单线段
                {
                    // 根据点击点距离线段两端的远近来判断延伸方向
                    // 距离较远的端点作为延伸起始点（即从较近的端点向较远的端点延伸）
                    ptStart = GMath.Distance(pt, nearestLine.PtEnd) > GMath.Distance(pt, nearestLine.PtSt) ? nearestLine.PtSt : nearestLine.PtEnd;
                    //创建从线段中点指向延伸起始点的方向向量
                    vec = new GuiDB.Vector(nearestLine.PtMid, ptStart);
                }
                else //多线段
                {
                    // 根据线段在整体中的位置（索引）确定延伸方向
                    if (index == 0)
                    {
                        vec = new GuiDB.Vector(nearestLine.PtMid, nearestLine.PtSt);
                        ptStart = nearestLine.PtSt;
                    }
                    else //最后一段线段，从终点延伸
                    {
                        vec = new GuiDB.Vector(nearestLine.PtMid, nearestLine.PtEnd);
                        ptStart = nearestLine.PtEnd;
                    }
                }

                //寻找延伸目标点
                DbPt result = null; //存储计算出的延伸结果点

                // 遍历所有目标图元，寻找与延伸线段相交的点
                foreach (var e in eles)
                {
                    //与目标图元的直线段求交
                    foreach (var l in e.Lines)
                    {
                        //计算当前线段延伸后与目标直线的交点
                        GFunc.GetLineExtendPoint(l, nearestLine, ptStart, vec, ref result);
                    }

                    //与目标图元的圆形求交
                    foreach (var c in e.Circles)
                    {
                        //计算当前线段延伸后与目标圆的交点
                        GFunc.GetLineExtendPoint(c, nearestLine, ptStart, vec, ref result);
                    }
                }

                //应用延伸结果
                if (result != null)
                {
                    if (lineToExtend.Count == 1) //单线段的情况控制点更新
                    {
                        //计算结果点到当前控制点的距离，确定替换哪个控制点
                        var dis1 = GMath.Distance(ConPts[0], result);
                        var dis2 = GMath.Distance(ConPts[2], result);

                        //替换距离较近的控制点
                        if (dis1 > dis2)
                        {
                            DbPt ptMid = GMath.GetMidPt(ConPts[0], result); //计算中点
                            ptMid.PtType = 2; //设置中点类型为普通中点
                            ReSetConPts(new List<DbPt> { ConPts[0], ptMid, result }); //替换第二个控制点
                        }
                        else
                        {
                            DbPt ptMid = GMath.GetMidPt(result, ConPts[2]); //计算中点
                            ptMid.PtType = 2; //设置中点类型为普通中点
                            ReSetConPts(new List<DbPt> { result, ptMid, ConPts[2] }); //替换第一个控制点
                        }
                    }
                    else //多段线的情况
                    {
                        var conPts = GFunc_CW.PtListEleCopy(ConPts); //复制当前控制点列表

                        if (index == 0) //延伸第一段线段
                        {

                            //移除并替换第一个控制点
                            conPts.RemoveAt(0); //移除第一个控制点
                            conPts.Insert(0, result); //在开头插入延伸结果点

                            DbPt ptMid = GMath.GetMidPt(conPts[0], conPts[2]); //计算中点
                            ptMid.PtType = 2;

                            conPts.RemoveAt(1); //移除第一个控制点
                            conPts.Insert(1, ptMid); //在开头插入延伸结果点

                            ReSetConPts(conPts);
                            return true;
                        }
                        else //延伸最后一段线段
                        {
                            DbPt ptMid = GMath.GetMidPt(conPts[conPts.Count - 3], result); //计算中点
                            ptMid.PtType = 2;

                            //移除并替换最后一个控制点
                            conPts.RemoveAt(conPts.Count - 1); //移除最后一个控制点
                            conPts.RemoveAt(conPts.Count - 1);
                            conPts.Add(ptMid);
                            conPts.Add(result);
                            ReSetConPts(conPts);
                            return true;
                        }
                    }
                }

                #endregion
            }
            else //处理弧线段延伸
            {
                #region 弧线段延伸逻辑

                //获取弧线的几何信息
                //返回逆时针圆弧
                DbLine arcToExtend = GMath.ArcAnLock(nearestLine);

                //计算弧线的圆心和半径
                var cenPt = GMath.GetArcCenter(arcToExtend.PtSt, arcToExtend.PtEnd, arcToExtend.PtMid);
                double radius = GMath.Distance(cenPt, arcToExtend.PtSt);

                //创建完整的圆形
                var circle = new DbCircle(cenPt, 2 * radius);

                //收集所有可能的延伸交点
                List<DbPt> extendPts = new List<DbPt>();

                // 遍历所有目标图元，寻找与延伸圆弧相交的点
                foreach (var e in eles)
                {
                    //与目标图元的直线段求交
                    foreach (var l in e.Lines)
                    {
                        GFunc.GetArcExtendPoint(l, circle, arcToExtend, extendPts);
                    }
                    //与目标图元的圆形求交
                    foreach (var c in e.Circles)
                    {
                        GFunc.GetArcExtendPoint(c, circle, arcToExtend, extendPts);
                    }
                }

                //处理弧线延伸结果
                if (extendPts.Count > 0)
                {
                    //计算点击点在弧线上的投影（用于确定延伸方向）
                    var footPt = GMath.ArcFootNearPt(arcToExtend, pt);

                    //执行弧线延伸计算，获取延伸后的新弧线
                    var result = GFunc.ArcExtend(null, arcToExtend, cenPt, radius, extendPts, footPt, null);


                    if (result != null)
                    {
                        //保持弧线方向的一致性
                        //如果弧线是顺时针，则将结果也设置为顺时针
                        if (GMath.IfArcClockwise(nearestLine))
                            result = result.Reverse(); //逆时针弧线变为顺时针

                        if (lineToExtend.Count == 1) //单弧线段情况
                        {
                            //直接用延伸后的弧线重置控制点
                            ResetConPts(new List<DbLine> { result });
                            ActAndCal2D();
                        }
                        else
                        {
                            //更新控制点：精确替换第一段弧线的控制点
                            var conPts = GFunc_CW.PtListEleCopy(ConPts);

                            if (index == 0) //延伸第一段弧线
                            {
                                //检查延伸是否有效（起点不能重叠）
                                if (result.PtSt.IfOverXY(nearestLine.PtSt)) return false;

                                //替换第一个弧线段的控制点
                                conPts[0] = result.PtSt.EleCopy(); //新的起点
                                conPts[1] = result.PtMid.EleCopy(); //新的弧线中点
                                conPts[1].PtType = 1; //确保弧线中点类型正确

                                ReSetConPts(conPts);
                                return true;
                            }
                            else
                            {
                                if (result.PtEnd.IfOverXY(nearestLine.PtEnd)) return false; //检查延伸是否有效

                                //替换最后一个弧线段的控制点
                                conPts[conPts.Count - 2] = result.PtMid.EleCopy(); //新的弧线中点
                                conPts[conPts.Count - 2].PtType = 1; //确保弧线中点类型正确
                                conPts[conPts.Count - 1] = result.PtEnd.EleCopy(); //新的终点
                                ReSetConPts(conPts);
                                return true;
                            }

                        }
                    }
                }
                #endregion
            }

            // 如果没有找到延伸点，返回false表示延伸失败
            return false;
        }

        /// <summary>
        /// 基于线的图元延伸到其他图元的线、圆时的行为:框选
        /// </summary>
        /// <param name="eles">目标图元集</param>
        /// <param name="rec">矩形框</param>
        /// <returns>返回true：该图元可延伸，false：该图元不可延伸</returns>
        public override bool EleExtendWithElesByRec(List<DbElement> eles, DbRec rec)
        {
            if (_close) //闭合铝单板不支持延伸
            {
                EBDB.Instance.UICmdLine("闭合铝单板不支持延伸!", null, null);
                return false;
            }

            var ptss = ConPts.Where(pt => pt.PtType != 2).ToList();
            GuiDB.Polyline pl = new GuiDB.Polyline(ptss, 1, false);
            if (pl.EleExtendWithElesByRec(eles, rec))
            {
                List<DbPt> pts = pl.GetOutPts();
                if (pts.Count > 1)
                {
                    AluminumPanel aluminum = new AluminumPanel(pts, this.Direction);
                    aluminum.LeftEdge = this.LeftEdge;
                    aluminum.RightEdge = this.RightEdge;
                    aluminum.Thickness = this.Thickness;
                    aluminum.EdgeDistance = this.EdgeDistance;
                    aluminum.SurfaceTreatment = this.SurfaceTreatment;
                    aluminum.Material = this.Material;
                    aluminum.ActCutCal2D3D();
                    _view.AddElement(aluminum);
                    _view.Delete(this);
                }
            }
            else
            {
                return false;
            }

            return false;
        }

        /// <summary>
        /// 基于线的图元被其他图元的线、圆裁剪时的行为：点选模式
        /// 使用其他图元作为"剪刀"来裁剪当前铝单板，可能产生多段墙体
        /// </summary>
        /// <param name="eles">目标图元集合，作为裁剪工具的图元</param>
        /// <param name="pt">点击点，用于确定裁剪位置和保留部分</param>
        /// <returns>返回true：该图元可裁剪，false：该图元不可裁剪</returns>
        public override bool EleCutWithElesByPt(List<DbElement> eles, DbPt pt)
        {
            //// 获取铝单板的基线用于裁切
            //List<DbLine> lineToBreak = this.LocationCurve;
            //if (lineToBreak == null || lineToBreak.Count == 0) return false;

            //// 根据点击点pt确定要延伸的线段(最近的线段)，返回该线段及其在整体中的索引
            //var needCutLine = GFunc.GetNearLine(lineToBreak, pt, out int index);
            //if (needCutLine == null || needCutLine.GetLong() < 1.0) return false;
            bool ifclose = _close; // 是否闭合铝单板
            List<DbPt> ptss = GetOutPts();
            if (ptss == null || ptss.Count < 2) return false; //确保有足够的控制点

            List<DbPt> conpts = GFunc_CW.PtListEleCopy(ptss); //复制当前控制点列表

            GFunc_CW.DelSamePts(conpts, false); //删除重复点

            // 基于控制点创建多义线，宽度为1（用于裁剪计算，不影响最终显示）
            GuiDB.Polyline pl = new GuiDB.Polyline(conpts, 1, ifclose);

            // 获取多义线的所有线段
            List<Db> getLines = new List<Db>();
            getLines.AddRange(pl.GetOutLines());

            // 【找到最近的线段】
            // 根据点击点找到最近的线段，这是裁剪操作的目标线段
            var dd = GFunc.GetNearDb(getLines, pt);
            if (dd == null)
            {
                return false;
            }

            // 【获取裁剪用的图元线段】
            // 从其他图元中提取用于裁剪的线段和圆弧
            GFunc_CW.GetAluminumPanelCutOrExtendLines(pl, eles, out List<DbLine> eleLines, out List<DbCircle> eleCircles);

            // 【执行线段裁剪】
            if (dd is DbLine)
            {
                DbLine l0 = dd as DbLine;
                // 使用点击点和其他图元的线段/圆弧对目标线段进行裁剪
                List<DbLine> cLine = GFunc_CW.CutLine(pt, l0, eleLines, eleCircles);

                if (cLine != null)
                {
                    // 裁剪成功，移除原线段，添加裁剪后的线段
                    getLines.Remove(dd);
                    getLines.AddRange(cLine);
                }
                else
                {
                    // 裁剪失败的处理
                    if (pl is GuiDB.Polyline)
                    {
                        // 如果是多义线，直接移除该线段（相当于删除该段）
                        getLines.Remove(dd);
                    }
                    else
                    {
                        return false;
                    }
                }
            }

            //重新构建铝单板
            if (getLines.Count > 0)
            {
                bool ifCloseJoin = true;  // 是否进行闭合连接处理
                var outLines = pl.GetOutLines();  // 原始输出线段

                // 初始化分组数据结构
                List<List<DbLine>> pLines = new List<List<DbLine>>() { new List<DbLine>() };


                // 将裁剪后的线段转换为DbLine列表
                List<DbLine> setLines0 = new List<DbLine>();
                setLines0.AddRange(getLines.ConvertAll(p => p as DbLine));

                // 【线段分组重建】
                // 将裁剪后的线段按照连续性进行分组，每组形成一个独立的铝单板
                for (int i = 0; i < outLines.Count; i++)
                {
                    var line0 = outLines[i];
                    bool ifOver = false;

                    // 检查当前原始线段是否与裁剪后的线段重叠
                    for (int j = 0; j < setLines0.Count; j++)
                    {
                        var line = setLines0[j];
                        if (GMath.IfOverlap(line, line0))
                        {
                            // 如果当前分组为空，或者与上一条线段连续，则加入当前分组
                            if (pLines.Last().Count == 0 || GMath.IfSglOverlap(pLines.Last().Last(), line0))
                            {
                                pLines.Last().Add(line0);
                            }
                            else
                            {
                                // 不连续，创建新的分组
                                pLines.Add(new List<DbLine> { line0 });
                            }
                            setLines0.Remove(line);
                            ifOver = true;
                            break;
                        }
                    }

                    // 【处理部分重叠的情况】
                    // 如果线段没有完全重叠，检查是否有部分重叠（起点或终点连接）
                    if (!ifOver)
                    {
                        DbLine stLine = null;   // 起点连接的线段
                        DbLine endLine = null;  // 终点连接的线段

                        // 查找与当前线段起点或终点连接的线段
                        for (int j = 0; j < setLines0.Count; j++)
                        {
                            var line = setLines0[j];
                            if (GMath.IfSglOverlap(line, line0))
                            {
                                // 检查各种连接情况
                                if (GMath.IfOverlap(line0.PtSt, line.PtSt))
                                {
                                    if (GMath.IfOnLiArc(line0, line.PtEnd))
                                    {
                                        stLine = line;
                                    }
                                }
                                else if (GMath.IfOverlap(line0.PtSt, line.PtEnd))
                                {
                                    if (GMath.IfOnLiArc(line0, line.PtSt))
                                    {
                                        stLine = line;
                                    }
                                }
                                else if (GMath.IfOverlap(line0.PtEnd, line.PtSt))
                                {
                                    if (GMath.IfOnLiArc(line0, line.PtEnd))
                                    {
                                        endLine = line;
                                    }
                                }
                                else if (GMath.IfOverlap(line0.PtEnd, line.PtEnd))
                                {
                                    if (GMath.IfOnLiArc(line0, line.PtSt))
                                    {
                                        endLine = line;
                                    }
                                }

                                // 如果找到了起点和终点连接的线段，停止搜索
                                if (stLine != null && endLine != null)
                                {
                                    break;
                                }
                            }
                        }

                        // 处理起点连接的线段
                        if (stLine != null)
                        {
                            pLines.Last().Add(stLine);
                            setLines0.Remove(stLine);
                        }

                        // 处理终点连接的线段（创建新分组）
                        if (endLine != null)
                        {
                            pLines.Add(new List<DbLine> { endLine });
                            setLines0.Remove(endLine);
                        }
                    }
                }

                // 【闭合连接处理】
                if (ifCloseJoin)
                {
                    // 如果原始多义线是闭合的，检查是否需要将首尾分组合并
                    if (pl.Closed)
                    {
                        if (GMath.IfSglOverlap(pLines.First().First(), pLines.Last().Last()))
                        {
                            pLines.Last().AddRange(pLines.First());
                            pLines.RemoveAt(0);
                        }
                    }

                    // 处理剩余的独立线段（可能是完全位于原始线段内部的线段）
                    foreach (var line in setLines0)
                    {
                        for (int i = 0; i < outLines.Count; i++)
                        {
                            if (GMath.IfOnLiArc(outLines[i], line.PtSt) && GMath.IfOnLiArc(outLines[i], line.PtEnd))
                            {
                                pLines.Add(new List<DbLine>() { line });
                                break;
                            }
                        }
                    }
                }

                // 【生成新的多义线】
                // 根据分组后的线段生成新的多义线列表
                var pls = GFunc_CW.GetPlByLines(pl, pLines);
                if (pls == null)
                {
                    return false;
                }

                // 【创建新的铝单板图元】
                if (pLines.Count > 0)
                {
                    foreach (var line in pls)
                    {
                        List<DbPt> pts = line.GetOutPts();
                        if (pts.Count > 1)
                        {
                            AluminumPanel aluminum = new AluminumPanel(pts, this.Direction);
                            aluminum.Thickness = this.Thickness;
                            aluminum.EdgeDistance = this.EdgeDistance;
                            aluminum.LeftEdge = this.LeftEdge;
                            aluminum.RightEdge = this.RightEdge;
                            aluminum.SurfaceTreatment = this.SurfaceTreatment;
                            aluminum.Material = this.Material;

                            // 执行2D到3D的裁剪计算
                            aluminum.ActCutCal2D3D();
                            _view.AddElement(aluminum);
                        }
                    }
                    _view.Delete(this);
                    return true;
                }
            }
            return false; // 如果没有生成新的多义线，返回false表示裁剪失败
        }

        /// <summary>
        /// 图元裁剪
        /// </summary>
        public override void EleCut()
        {
            ActAndCal2D();
        }

        /// <summary>
        /// 基于线的图元，被其他图元的线、圆裁剪时（框选），该方法被调用
        /// </summary>
        /// <_param name="eles"></_param>
        /// <_param name="rec"></_param>
        /// <returns></returns>
        public override bool EleCutWithElesByRec(List<DbElement> eles, DbRec rec)
        {
            var recLines = new List<DbLine>() { rec.Line_L, rec.Line_R, rec.Line_D, rec.Line_U };

            // 获取铝单板的基线用于裁切
            List<DbLine> lineToBreak = this.LocationCurve;
            if (lineToBreak == null || lineToBreak.Count == 0) return false;

            //寻找铝单板基准线段与矩形框的交点
            List<DbPt> pts = new List<DbPt>();

            //遍历矩形框的每条边，寻找与铝单板基准线段的交点
            foreach (var line in lineToBreak)
            {
                var pps = GFunc_CW.FindCrossPtLineLines(line, recLines);
                if (pps != null)
                {
                    // 将找到的交点添加到交点集合中
                    pts.Add(pps);
                }
            }

            //执行裁切
            //如果找到了交点，使用第一个交点裁切
            if (pts.Count > 0)
            {
                return this.EleCutWithElesByPt(eles, pts[0]);
            }
            return false; //如果没有找到交点，返回false表示裁剪失败
        }

        /// <summary>
        /// 与另一个图元发生倒角时的行为
        /// </summary>
        /// <param name="thisPt">倒角时当前图元的点击点</param>
        /// <param name="ele">参与倒角的另一个图元</param>
        /// <param name="pt">倒角时另一个图元的点击点</param>
        /// <param name="rad">圆角半径，0为倒直角</param>
        /// <returns>返回true：该图元可倒角，false：该图元不可倒角</returns>
        public override bool EleFilletWithEle(DbPt thisPt, DbElement ele, DbPt pt, double rad = 0)
        {
            return true;
        }


        #endregion

        #region 自定义函数
        /// <summary>
        /// 设置铝单板的控制点，自带事务
        /// </summary>
        /// <_param name="pts"></_param>    
        public void ResetConPts(List<DbPt> pts)
        {
            if (pts.Count == 0) return;
            var preConPts = ConPts.Select(p => p.EleCopy()).ToList();
            using (new Transaction("修改铝单板控制点"))
            {
                TransManager.Instance().Push(_ =>
                {
                    ResetConPts(preConPts);
                }, "");
            }
            ConPts = pts;
            ActCutCal2D3D();
            EBDB.Instance.UIReDrawActiv();

        }

        /// <summary>
        /// 设置铝单板的控制点，自带事务
        /// </summary>
        /// <_param name="ls"></_param>
        public void ResetConPts(List<DbLine> ls)
        {
            if (ls.Count == 0) return;
            var preConPts = ConPts.Select(p => p.EleCopy()).ToList();

            var pts = GMath.GetPtsOfLines(ls, false, true);

            using (new Transaction("修改铝单板控制线"))
            {
                TransManager.Instance().Push(_ =>
                {
                    ResetConPts(preConPts);
                }, "");

            }
            ConPts = pts;
        }

        /// <summary>
        /// 返回使用材料的Name
        /// </summary>
        /// <returns></returns>
        public HashSet<string> GetMaterail()
        {
            HashSet<string> result = new HashSet<string>();
            if (_material != null && !string.IsNullOrEmpty(_material.Name))
            {
                result.Add(_material.Name);
            }
            else
            {
                result.Add("默认材质");
            }
            return result;
        }

        /// <summary>
        /// 获取材料名称（单个字符串）
        /// </summary>
        /// <returns></returns>
        public string GetMaterailName()
        {
            if (_material != null && !string.IsNullOrEmpty(_material.Name))
            {
                return _material.Name;
            }
            return "默认材质";
        }

        /// <summary>
        /// 返回表面处理方式的Name
        /// </summary>
        /// <returns></returns>
        public string GetSurfaceTreatmentName()
        {
            return DbSurfaceTreatment.GetDefaultName(_surfaceTreatment);
        }

        ///// <summary>
        ///// 返回表面处理方式的Name（用于枚举类型）
        ///// </summary>
        ///// <param name="treatmentType">表面处理枚举</param>
        ///// <returns></returns>
        //public static HashSet<string> GetSurfaceTreatment(SurfaceTreatmentType treatmentType)
        //{
        //    HashSet<string> result = new HashSet<string>();
        //    result.Add(DbSurfaceTreatment.GetDefaultName(treatmentType));
        //    return result;
        //}

        ///// <summary>
        ///// 获取表面处理名称（用于枚举类型，单个字符串）
        ///// </summary>
        ///// <param name="treatmentType">表面处理枚举</param>
        ///// <returns></returns>
        //public static string GetSurfaceTreatmentName(SurfaceTreatmentType treatmentType)
        //{
        //    return DbSurfaceTreatment.GetDefaultName(treatmentType);
        //}
        #endregion

        #region IConvertibleToBlock接口实现

        /// <summary>
        /// 获取图元的线段数据（实现IConvertibleToBlock接口）
        /// </summary>
        /// <returns>线段列表</returns>
        public List<DbLine> GetLines()
        {
            if (Lines != null && Lines.Count > 0)
            {
                // 深度复制所有线段
                return Lines.Select(line => line.EleCopy()).ToList();
            }
            return new List<DbLine>();
        }

        /// <summary>
        /// 获取图元的材质名称（实现IConvertibleToBlock接口）
        /// </summary>
        /// <returns>材质名称</returns>
        string ElementToBlockCmd.IConvertibleToBlock.GetMaterialName()
        {
            return GetMaterailName();
        }

        /// <summary>
        /// 获取图元的表面处理名称（实现IConvertibleToBlock接口）
        /// </summary>
        /// <returns>表面处理名称</returns>
        string ElementToBlockCmd.IConvertibleToBlock.GetSurfaceTreatmentName()
        {
            return GetSurfaceTreatmentName();
        }

        /// <summary>
        /// 获取图元的厚度（实现IConvertibleToBlock接口）
        /// </summary>
        /// <returns>厚度值</returns>
        double ElementToBlockCmd.IConvertibleToBlock.GetThickness()
        {
            return Thickness;
        }

        /// <summary>
        /// 获取图元的类型名称（实现IConvertibleToBlock接口）
        /// </summary>
        /// <returns>类型名称</returns>
        string ElementToBlockCmd.IConvertibleToBlock.GetElementTypeName()
        {
            return "铝单板";
        }

        /// <summary>
        /// 生成自定义块名（实现IConvertibleToBlock接口）
        /// 铝单板使用：厚度mm厚+表面处理+铝单板+UUID
        /// </summary>
        /// <param name="uuid">8位UUID用于确保唯一性</param>
        /// <returns>自定义块名</returns>
        string ElementToBlockCmd.IConvertibleToBlock.GenerateCustomBlockName(string uuid)
        {
            string materialName = GetMaterailName();
            string surfaceTreatmentName = GetSurfaceTreatmentName();
            
            // 铝单板的命名规则：厚度mm厚+表面处理+铝单板+UUID
            return $"{Thickness}mm厚{surfaceTreatmentName}铝单板{uuid}";
        }

        #endregion

    }
