# 定位点控制逻辑技术文档

## 概述

定位点控制逻辑是钢矩管图元的核心功能，通过定位点来实现精确的参数化控制。本文档详细描述了定位点控制逻辑的设计原理、实现方法和使用指南。

## 核心特性

### 1. 预设位置向外偏移30mm

所有预设定位点位置都向外偏移30mm，便于用户操作和视觉识别。

#### 预设位置定义

| 定位点类型 | 基础偏移 | 实际偏移 |
|------------|----------|----------|
| 几何中心 | (0, 0) | (0, 0) |
| 左下角 | (-w/2, -h/2) | (-w/2-30, -h/2-30) |
| 下边中点 | (0, -h/2) | (0, -h/2-30) |
| 右下角 | (w/2, -h/2) | (w/2+30, -h/2-30) |
| 左中点 | (-w/2, 0) | (-w/2-30, 0) |
| 右中点 | (w/2, 0) | (w/2+30, 0) |
| 左上角 | (-w/2, h/2) | (-w/2-30, h/2+30) |
| 上边中点 | (0, h/2) | (0, h/2+30) |
| 右上角 | (w/2, h/2) | (w/2+30, h/2+30) |

其中：w = 矩管宽度，h = 矩管高度

#### 实现代码

```csharp
public void UpdateOffsetFromType(double elementWidth, double elementHeight)
{
    double halfWidth = elementWidth / 2.0;
    double halfHeight = elementHeight / 2.0;
    const double PRESET_OFFSET = 30.0; // 预设位置向外偏移30mm

    switch (_locatorType)
    {
        case LocatorPointType.BottomLeft:
            _offsetX = -halfWidth - PRESET_OFFSET;
            _offsetY = -halfHeight - PRESET_OFFSET;
            break;
        // ... 其他预设位置
    }
}
```

### 2. 智能拉伸控制

根据定位点的位置，智能判断哪些边可以被拉伸，只有远离定位点的一侧才能被拉动。

#### 控制原理

- **定位点在左侧**：左边不可拉伸，右边可拉伸
- **定位点在右侧**：右边不可拉伸，左边可拉伸
- **定位点在上侧**：上边不可拉伸，下边可拉伸
- **定位点在下侧**：下边不可拉伸，上边可拉伸
- **定位点在中心**：所有边都可拉伸

#### 位置判断逻辑

```csharp
public LocatorRelativePosition GetRelativePosition(double elementWidth, double elementHeight)
{
    double halfWidth = elementWidth / 2.0;
    double halfHeight = elementHeight / 2.0;

    // 判断定位点在矩管的哪一侧
    bool isLeft = _offsetX < -halfWidth * 0.5;
    bool isRight = _offsetX > halfWidth * 0.5;
    bool isTop = _offsetY > halfHeight * 0.5;
    bool isBottom = _offsetY < -halfHeight * 0.5;

    return new LocatorRelativePosition
    {
        IsLeft = isLeft,
        IsRight = isRight,
        IsTop = isTop,
        IsBottom = isBottom,
        IsHorizontalCenter = !isLeft && !isRight,
        IsVerticalCenter = !isTop && !isBottom
    };
}
```

#### 智能拉伸实现

```csharp
// 获取定位点相对位置信息
var relativePos = _locatorPoint.GetRelativePosition(_width, _height);

// 左边中点：宽度调整
if (ConPts[edgeMidStartIndex].Status == 1)
{
    // 智能拉伸：只有当定位点不在左侧时才允许左边拉伸
    if (!relativePos.IsLeft)
    {
        // 执行拉伸逻辑
        // 如果定位点在右侧，需要调整矩管位置保持定位点固定
        if (relativePos.IsRight)
        {
            double widthChange = newWidth - _width;
            _insertPt.X -= widthChange / 2; // 向左移动矩管中心
        }
    }
}
```

### 3. 距离调整控制

调整定位点与矩管之间的距离时，定位点位置保持固定，矩管整体移动。

#### 控制原理

- **定位点固定**：定位点的世界坐标保持不变
- **矩管移动**：通过调整插入点位置来移动矩管
- **相对关系**：定位点与矩管的相对位置关系发生改变

#### 实现逻辑

```csharp
public double LocatorDistanceX
{
    get => _locatorPoint.OffsetX;
    set
    {
        if (Math.Abs(_locatorPoint.OffsetX - value) > 0.001)
        {
            // 计算图元需要移动的距离（定位点固定，图元移动）
            double deltaX = value - _locatorPoint.OffsetX;
            _locatorPoint.OffsetX = value;
            
            // 移动插入点
            _insertPt.X -= deltaX;
            
            ActCutCal2D3D();
        }
    }
}
```

### 4. 拖拽行为控制

移动定位点时，定位点和矩管同时移动，保持相对位置关系不变。

#### 控制原理

- **同步移动**：定位点和矩管作为一个整体移动
- **关系保持**：定位点与矩管的相对位置关系保持不变
- **简化操作**：用户拖拽定位点等同于拖拽整个图元

#### 实现逻辑

```csharp
// 检查定位点是否被拖拽
if (locatorPointIndex > 0 && ConPts.Count > locatorPointIndex && ConPts[locatorPointIndex].Status == 1)
{
    // 定位点被拖拽：定位点和矩管同时移动
    // 直接移动整个图元（包括定位点和矩管）
    EleMove(X, Y);
    return;
}
```

## 使用场景

### 场景1：精确定位

```csharp
// 创建定位点在左下角的矩管
SteelTubeV2 tube = new SteelTubeV2(insertPt, 60, 120, 3, 6, 
    LocatorPoint.LocatorPointType.BottomLeft, 0);

// 定位点自动偏移到 (-60, -90) 位置
// 只能拉伸右边和上边
```

### 场景2：参数化控制

```csharp
// 调整定位点距离，矩管整体移动
tube.LocatorDistanceX = 50;  // 定位点固定，矩管向左移动50mm
tube.LocatorDistanceY = -30; // 定位点固定，矩管向下移动60mm
```

### 场景3：智能拉伸

```csharp
// 定位点在下边中点时
// 可以拉伸：左边、右边、上边
// 不可拉伸：下边（定位点所在侧）
```

## 技术架构

### 类结构

```
LocatorPoint (定位点类)
├── LocatorPointType (定位点类型枚举)
├── LocatorRelativePosition (相对位置信息)
├── UpdateOffsetFromType() (更新预设偏移)
├── GetRelativePosition() (获取相对位置)
└── HandleDrag() (处理拖拽)

SteelTubeV2 (钢矩管类)
├── LocatorPosition (定位点位置属性)
├── LocatorDistanceX/Y (定位点距离属性)
├── EleMove_s() (智能拉伸控制)
└── OnLocatorDragged() (拖拽事件处理)
```

### 事件系统

```csharp
// 定位点类事件
public event Action OnLocatorTypeChanged;     // 类型改变
public event Action OnOffsetChanged;          // 偏移改变
public event Action<double, double> OnLocatorDragged; // 拖拽事件

// 矩管类事件处理
private void OnLocatorChanged() => ActCutCal2D3D();
private void OnLocatorDragged(double deltaX, double deltaY) => 
    { _insertPt.X += deltaX; _insertPt.Y += deltaY; ActCutCal2D3D(); }
```

## 测试验证

### 测试用例

1. **预设位置测试**：验证所有预设位置的偏移值正确
2. **智能拉伸测试**：验证不同定位点位置的拉伸控制逻辑
3. **距离调整测试**：验证距离调整时矩管移动的正确性
4. **拖拽行为测试**：验证拖拽时同步移动的正确性
5. **综合场景测试**：验证复杂场景下的综合表现

### 运行测试

```csharp
// 运行所有测试
TestLocatorControlLogic.RunAllTests();

// 运行特定测试
TestLocatorControlLogic.TestPresetPositionOffset();
TestLocatorControlLogic.TestSmartStretchControl();
TestLocatorControlLogic.TestDistanceAdjustmentControl();
TestLocatorControlLogic.TestDragBehavior();
TestLocatorControlLogic.TestComprehensiveScenario();
```

## 性能优化

### 计算优化

1. **缓存相对位置**：避免重复计算定位点相对位置
2. **事件节流**：避免频繁的重绘和重计算
3. **增量更新**：只更新变化的部分

### 内存优化

1. **对象复用**：复用临时计算对象
2. **延迟计算**：只在需要时计算复杂几何
3. **事件解绑**：及时解绑不需要的事件

## 扩展性设计

### 新定位点类型

```csharp
// 添加新的定位点类型
public enum LocatorPointType
{
    // 现有类型...
    QuarterPoint1 = 10,  // 1/4点
    QuarterPoint2 = 11,  // 3/4点
    // 更多自定义类型...
}
```

### 新控制逻辑

```csharp
// 扩展相对位置判断
public class LocatorRelativePosition
{
    // 现有属性...
    public bool IsInQuarter1 { get; set; }
    public bool IsInQuarter2 { get; set; }
    // 更多位置信息...
}
```

## 最佳实践

### 使用建议

1. **合理选择定位点类型**：根据设计需求选择合适的预设位置
2. **充分利用智能拉伸**：利用智能拉伸控制提高建模效率
3. **灵活使用距离调整**：通过距离调整实现精确定位
4. **注意拖拽行为**：理解拖拽定位点的同步移动特性

### 常见问题

1. **定位点不可见**：检查IsEnabled和IsVisible属性
2. **拉伸不生效**：检查定位点相对位置是否允许拉伸
3. **距离调整异常**：检查偏移值设置是否正确
4. **拖拽行为异常**：检查控制点状态和事件绑定

## 版本历史

- **v6.0**：初始版本，实现基础定位点控制逻辑
- **v6.1**：添加预设位置30mm偏移
- **v6.2**：实现智能拉伸控制
- **v6.3**：完善距离调整和拖拽行为
- **v6.4**：优化性能和稳定性

## 总结

定位点控制逻辑为钢矩管图元提供了强大而直观的参数化控制能力，通过预设位置、智能拉伸、距离调整和拖拽行为四个核心特性，实现了专业级的建筑设计工具功能。该系统具有良好的扩展性和可维护性，为后续功能扩展奠定了坚实基础。 