# 矩管代码优化修改说明

## 修改概述

根据要求对矩管代码进行了以下核心修改：

1. **插入点逻辑简化**：插入点固定在矩管下边中点往下偏移30mm处，不再进行插入点和几何中点的判定
2. **控制点结构调整**：下边中点存储在ConPts[0]，以下边中点为基点生成其他控制点
3. **宽高参数调整逻辑**：宽度左右同时扩大，高度向上扩大

## 详细修改内容

### 1. 插入点和下边中点计算逻辑

**原来的复杂逻辑**：
- 通过象限判断算法计算几何中心位置
- 需要考虑左右偏移和上下偏移的大小关系

**修改后的简化逻辑**：
```csharp
/// <summary>
/// 计算下边中点 - 基于插入点固定偏移30mm
/// 插入点固定在矩管下边中点往下30mm处，不再进行插入点和几何中点的判定
/// </summary>
public void CalcuBottomCenter()
{
    // 插入点位置固定：矩管下边中点往下偏移30mm处
    // 因此下边中点 = 插入点向上偏移30mm
    _bottomCenterPt = _insertPt.Move(0, 30);
}

/// <summary>
/// 计算几何中心点 - 基于下边中点
/// </summary>
public void CalcuPtcenter()
{
    // 几何中心 = 下边中点向上偏移高度的一半
    _centerPt = _bottomCenterPt.Move(0, _height / 2.0);
}
```

### 2. 控制点结构调整

**原来的结构**：
- ConPts[0]: 几何中心
- ConPts[1-4]: 边中点

**修改后的结构**：
- ConPts[0]: 下边中点（基准点）
- ConPts[1]: 上边中点
- ConPts[2]: 右边中点  
- ConPts[3]: 左边中点
- ConPts[4]: 几何中心

**关键代码**：
```csharp
/// <summary>
/// 计算所有控制点 - 基于下边中点位置
/// 下边中点存储在ConPts[0]，以下边中点为基点生成其他控制点
/// </summary>
public void CalcuPts()
{
    // 清除所有控制点
    ConPts.Clear();

    // 确保下边中点作为第一个控制点 ConPts[0]
    DbPt bottomCenter = _bottomCenterPt.EleCopy();
    bottomCenter.PtType = 2; // 标记为边中点
    ConPts.Add(bottomCenter);

    double halfWidth = _width / 2.0;
    double halfHeight = _height / 2.0;

    // 以下边中点为基点生成其他控制点
    // ConPts[1]: 上边中点
    DbPt topMid = new DbPt(bottomCenter.X, bottomCenter.Y + _height);
    topMid.PtType = 2; // 标记为边中点
    ConPts.Add(topMid);

    // ConPts[2]: 右边中点
    DbPt rightMid = new DbPt(bottomCenter.X + halfWidth, bottomCenter.Y + halfHeight);
    rightMid.PtType = 2; // 标记为边中点
    ConPts.Add(rightMid);

    // ConPts[3]: 左边中点
    DbPt leftMid = new DbPt(bottomCenter.X - halfWidth, bottomCenter.Y + halfHeight);
    leftMid.PtType = 2; // 标记为边中点
    ConPts.Add(leftMid);

    // ConPts[4]: 几何中心点
    DbPt geometryCenter = new DbPt(bottomCenter.X, bottomCenter.Y + halfHeight);
    geometryCenter.PtType = 1; // 标记为几何中心
    ConPts.Add(geometryCenter);
    
    // 更新几何中心点引用
    _centerPt = geometryCenter.EleCopy();
    
    // 其他逻辑保持不变...
}
```

### 3. 拉伸逻辑调整

**宽度调整**：左右同时扩大（变化量乘以2）
**高度调整**：向上扩大（下边中点保持不变）

```csharp
// 右边中点：宽度调整（左右同时扩大）
else if (ConPts[2].Status == 1)
{
    double localDeltaX = CalculateLocalDeltaWithTransform(X, Y, false);
    ConPts[2].MoveSelf(X, Y);

    DbPt dragVector = new DbPt(X, Y);
    double dotProduct = dragVector.X;

    // 宽度变化：左右同时扩大，所以变化量要乘以2
    double widthChange = Math.Abs(localDeltaX) * (dotProduct > 0 ? 2 : -2);
    _width = Math.Max(10, _width + widthChange);

    RecalculateFromBottomCenter();
}
```

### 4. 相关方法的适配修改

- **移动方法**：更新下边中点和几何中心点的引用
- **旋转方法**：正确更新控制点引用
- **镜像方法**：正确更新控制点引用
- **变换应用**：使用几何中心作为变换中心
- **控制点提取**：使用正确的控制点索引

## 修改优势

1. **逻辑简化**：去除了复杂的象限判断算法，插入点位置固定且明确
2. **结构清晰**：下边中点作为基准点存储在ConPts[0]，便于理解和维护
3. **行为一致**：宽度左右同时扩大，高度向上扩大，符合用户预期
4. **代码维护性**：减少了复杂的偏移参数计算，提高了代码可读性

## 测试建议

建议测试以下场景：
1. 创建矩管并验证插入点和下边中点的关系
2. 修改宽度参数，验证左右同时扩大
3. 修改高度参数，验证向上扩大
4. 拖拽控制点进行拉伸操作
5. 进行旋转、镜像等几何变换操作
